@echo off
echo ========================================
echo Node.js Installation Verification
echo ========================================
echo.

echo Testing Node.js installation...
echo.

REM Test with full path
echo [1/3] Testing with full path...
"C:\Program Files\nodejs\node.exe" --version
if %errorlevel% equ 0 (
    echo ✅ Node.js executable found and working
) else (
    echo ❌ Node.js executable not working
)

echo.
echo [2/3] Testing npm with full path...
"C:\Program Files\nodejs\npm.cmd" --version
if %errorlevel% equ 0 (
    echo ✅ npm executable found and working
) else (
    echo ❌ npm executable not working
)

echo.
echo [3/3] Testing PATH environment...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js is in PATH - ready to use!
    for /f "tokens=*" %%i in ('node --version') do echo    Version: %%i
) else (
    echo ⚠️  Node.js not in PATH - restart terminal needed
    echo    Node.js is installed but PATH needs refresh
)

echo.
echo ========================================
echo Verification Complete
echo ========================================
echo.
echo 💡 If Node.js is not in PATH:
echo    1. Close this terminal completely
echo    2. Reopen a fresh terminal
echo    3. Run start.bat again
echo.
pause
