@echo off
echo Starting Google Veo AI Video Generator...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org/
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist .env (
    echo Warning: .env file not found
    echo Please copy .env.example to .env and configure your Google Cloud settings
    echo.
    if exist .env.example (
        echo Copying .env.example to .env...
        copy .env.example .env
        echo Please edit .env file with your Google Cloud configuration
        pause
    )
)

REM Install frontend dependencies if node_modules doesn't exist
if not exist node_modules (
    echo Installing frontend dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo Error: Failed to install frontend dependencies
        pause
        exit /b 1
    )
)

REM Create and activate virtual environment if it doesn't exist
if not exist backend\venv (
    echo Creating Python virtual environment...
    cd backend
    python -m venv venv
    if %errorlevel% neq 0 (
        echo Error: Failed to create virtual environment
        cd ..
        pause
        exit /b 1
    )
    cd ..
)

REM Install backend dependencies in virtual environment
echo Installing backend dependencies in virtual environment...
cd backend
call venv\Scripts\activate.bat
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo Error: Failed to install backend dependencies
    call venv\Scripts\deactivate.bat
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo Starting backend server...
start "Backend Server" cmd /k "cd backend && call venv\Scripts\activate.bat && python main.py"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

echo Starting frontend development server...
start "Frontend Server" cmd /k "npm run dev"

echo.
echo Both servers are starting...
echo Backend: http://localhost:8000
echo Frontend: http://localhost:3000
echo.
echo Press any key to exit...
pause >nul
