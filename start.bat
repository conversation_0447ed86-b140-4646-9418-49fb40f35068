@echo off
setlocal enabledelayedexpansion
echo ========================================
echo Google Veo AI Video Generator - Startup
echo ========================================
echo.

REM Check if Node.js is installed
echo [1/8] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Node.js is not installed or not in PATH
    echo    Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js found: !NODE_VERSION!

REM Check if Python is installed
echo.
echo [2/8] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Python is not installed or not in PATH
    echo    Please install Python from https://python.org/
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ✅ Python found: !PYTHON_VERSION!

REM Check if .env file exists
echo.
echo [3/8] Checking environment configuration...
if not exist .env (
    echo ⚠️  Warning: .env file not found
    echo    Please copy .env.example to .env and configure your Google Cloud settings
    echo.
    if exist .env.example (
        echo    Copying .env.example to .env...
        copy .env.example .env >nul
        if %errorlevel% equ 0 (
            echo ✅ Created .env file from template
            echo ⚠️  Please edit .env file with your Google Cloud configuration before continuing
        ) else (
            echo ❌ Failed to copy .env.example
        )
        echo.
        pause
    ) else (
        echo ❌ .env.example file not found
        echo    Please create .env file manually with your Google Cloud configuration
        echo.
        pause
    )
) else (
    echo ✅ Environment file (.env) found
)

REM Install frontend dependencies if node_modules doesn't exist
echo.
echo [4/8] Checking frontend dependencies...
if not exist node_modules (
    echo ⚠️  Frontend dependencies not found, installing...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Error: Failed to install frontend dependencies
        echo    Please check your internet connection and npm configuration
        echo.
        pause
        exit /b 1
    )
    echo ✅ Frontend dependencies installed successfully
) else (
    echo ✅ Frontend dependencies (node_modules) found
)

REM Virtual Environment Management
echo.
echo [5/8] Checking Python virtual environment...

REM Check if virtual environment exists
if not exist backend\venv (
    echo ⚠️  Virtual environment not found, creating...
    cd backend
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ Error: Failed to create virtual environment
        echo    Please ensure Python is properly installed and has venv module
        cd ..
        echo.
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created successfully
    cd ..
) else (
    echo ✅ Virtual environment found at backend\venv
)

REM Test virtual environment activation
echo.
echo [6/8] Testing virtual environment activation...
cd backend
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ Error: Failed to activate virtual environment
    echo    Virtual environment may be corrupted, please delete backend\venv and try again
    cd ..
    echo.
    pause
    exit /b 1
)
echo ✅ Virtual environment activated successfully

REM Check if requirements.txt exists
if not exist requirements.txt (
    echo ❌ Error: requirements.txt not found in backend directory
    echo    Please ensure requirements.txt exists
    call venv\Scripts\deactivate.bat >nul 2>&1
    cd ..
    echo.
    pause
    exit /b 1
)

REM Check if dependencies need to be installed/updated
echo.
echo [7/8] Checking Python dependencies...
set NEED_INSTALL=0

REM Check if pip freeze shows any packages (simple dependency check)
pip freeze >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Pip not working properly in virtual environment
    set NEED_INSTALL=1
) else (
    REM Check if key packages are installed
    pip show fastapi >nul 2>&1
    if %errorlevel% neq 0 (
        echo ⚠️  Key dependencies missing, will install...
        set NEED_INSTALL=1
    ) else (
        echo ✅ Dependencies appear to be installed
        REM Check if requirements.txt is newer than last install
        if exist .last_install (
            for %%i in (requirements.txt) do set REQ_TIME=%%~ti
            for %%i in (.last_install) do set INSTALL_TIME=%%~ti
            REM Simple comparison - if requirements.txt is newer, reinstall
            if "!REQ_TIME!" gtr "!INSTALL_TIME!" (
                echo ⚠️  requirements.txt has been updated, reinstalling...
                set NEED_INSTALL=1
            )
        ) else (
            echo ⚠️  No install timestamp found, installing dependencies...
            set NEED_INSTALL=1
        )
    )
)

REM Install dependencies if needed
if !NEED_INSTALL! equ 1 (
    echo    Installing/updating Python dependencies...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ Error: Failed to install Python dependencies
        echo    Please check your internet connection and requirements.txt file
        call venv\Scripts\deactivate.bat >nul 2>&1
        cd ..
        echo.
        pause
        exit /b 1
    )
    REM Create timestamp file to track last install
    echo. > .last_install
    echo ✅ Python dependencies installed/updated successfully
) else (
    echo ✅ Python dependencies are up to date
)

cd ..

REM Start the servers
echo.
echo [8/8] Starting application servers...
echo.

REM Start backend server with virtual environment activated
echo 🚀 Starting backend server (with virtual environment)...
start "Google Veo Backend Server" cmd /k "title Google Veo Backend Server && cd /d "%~dp0backend" && call venv\Scripts\activate.bat && echo ✅ Virtual environment activated && echo 🚀 Starting FastAPI server... && python main.py"

REM Wait a moment for backend to start
echo    Waiting for backend to initialize...
timeout /t 4 /nobreak >nul

REM Start frontend development server
echo 🚀 Starting frontend development server...
start "Google Veo Frontend Server" cmd /k "title Google Veo Frontend Server && cd /d "%~dp0" && echo 🚀 Starting Vite development server... && npm run dev"

REM Final status
echo.
echo ========================================
echo ✅ Google Veo AI Video Generator Started
echo ========================================
echo.
echo 🌐 Application URLs:
echo    • Backend API: http://localhost:8000
echo    • Frontend UI:  http://localhost:3000
echo    • API Docs:    http://localhost:8000/docs
echo.
echo 📋 Server Status:
echo    • Backend: Starting with Python virtual environment
echo    • Frontend: Starting with Vite development server
echo.
echo 💡 Tips:
echo    • Wait a few seconds for both servers to fully start
echo    • Check the server windows for any error messages
echo    • The frontend will automatically open in your browser
echo.
echo 🔧 Troubleshooting:
echo    • If servers fail to start, check the individual server windows
echo    • Ensure .env file is properly configured
echo    • Run 'python setup-check.py' to verify setup
echo.
echo Press any key to close this window...
pause >nul
