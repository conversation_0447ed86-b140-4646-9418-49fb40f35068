@echo off
setlocal enabledelayedexpansion
echo ========================================
echo Google Veo AI Video Generator - Startup
echo ========================================
echo.

REM Check if Node.js is installed
echo [1/8] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Node.js is not installed or not in PATH
    echo    Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js found: !NODE_VERSION!

REM Check if Python is installed
echo.
echo [2/8] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Python is not installed or not in PATH
    echo    Please install Python from https://python.org/
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ✅ Python found: !PYTHON_VERSION!

REM Check if .env file exists
echo.
echo [3/8] Checking environment configuration...
if not exist .env (
    echo ⚠️  Warning: .env file not found
    echo    Please copy .env.example to .env and configure your Google Cloud settings
    echo.
    if exist .env.example (
        echo    Copying .env.example to .env...
        copy .env.example .env >nul
        if %errorlevel% equ 0 (
            echo ✅ .env file created from template
            echo ⚠️  Please edit .env file with your Google Cloud configuration before continuing
        ) else (
            echo ❌ Failed to copy .env.example to .env
        )
        echo.
        pause
    ) else (
        echo ❌ .env.example file not found
        echo    Please create .env file manually with your Google Cloud configuration
        echo.
        pause
    )
) else (
    echo ✅ Environment configuration file found
)

REM Install frontend dependencies if node_modules doesn't exist
echo.
echo [4/8] Checking frontend dependencies...
if not exist node_modules (
    echo 📦 Installing frontend dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Error: Failed to install frontend dependencies
        echo    Please check your internet connection and try again
        echo.
        pause
        exit /b 1
    )
    echo ✅ Frontend dependencies installed successfully
) else (
    echo ✅ Frontend dependencies already installed
)

REM Virtual Environment Management
echo.
echo [5/8] Managing Python virtual environment...

REM Check if virtual environment exists
if not exist backend\venv (
    echo 🐍 Creating Python virtual environment...
    cd backend
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ Error: Failed to create virtual environment
        echo    Please ensure Python is properly installed and has venv module
        echo    You may need to install python-venv package on some systems
        cd ..
        echo.
        pause
        exit /b 1
    )
    cd ..
    echo ✅ Virtual environment created successfully
) else (
    echo ✅ Virtual environment already exists
)

REM Test virtual environment activation
echo.
echo [6/8] Testing virtual environment activation...
cd backend
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ Error: Failed to activate virtual environment
    echo    The virtual environment may be corrupted. Please delete backend\venv and try again
    cd ..
    echo.
    pause
    exit /b 1
)
echo ✅ Virtual environment activated successfully

REM Check if dependencies need to be installed/updated
echo.
echo [7/8] Checking Python dependencies...

REM Check if requirements.txt is newer than the last install or if packages are missing
set NEED_INSTALL=0

REM Check if pip-installed packages list exists (we'll create a marker file)
if not exist venv\.dependencies_installed (
    set NEED_INSTALL=1
    echo 📦 First-time dependency installation required
) else (
    REM Check if requirements.txt is newer than our marker file
    for %%i in (requirements.txt) do set REQ_DATE=%%~ti
    for %%i in (venv\.dependencies_installed) do set MARKER_DATE=%%~ti

    REM Simple check - if requirements.txt exists and marker is older, reinstall
    if exist requirements.txt (
        echo 🔍 Checking if dependencies need updating...
        REM For simplicity, we'll check if key packages are importable
        python -c "import fastapi, uvicorn, google.cloud.aiplatform" >nul 2>&1
        if !errorlevel! neq 0 (
            set NEED_INSTALL=1
            echo 📦 Missing or corrupted dependencies detected
        ) else (
            echo ✅ Dependencies appear to be installed correctly
        )
    )
)

REM Install dependencies if needed
if !NEED_INSTALL! equ 1 (
    echo 📦 Installing/updating Python dependencies...
    echo    This may take a few minutes...

    REM Try to upgrade pip, but don't fail if it has permission issues
    echo    Checking pip version...
    pip install --upgrade pip >nul 2>&1
    if !errorlevel! neq 0 (
        echo ⚠️  Note: Could not upgrade pip (permission issue), continuing with current version
        echo    This is usually not a problem for dependency installation
    ) else (
        echo ✅ Pip upgraded successfully
    )

    pip install -r requirements.txt
    if !errorlevel! neq 0 (
        echo ❌ Error: Failed to install backend dependencies
        echo    Please check your internet connection and requirements.txt file
        call venv\Scripts\deactivate.bat
        cd ..
        echo.
        pause
        exit /b 1
    )

    REM Create marker file to indicate successful installation
    echo Dependencies installed on %date% %time% > venv\.dependencies_installed
    echo ✅ Backend dependencies installed/updated successfully
) else (
    echo ✅ Backend dependencies are up to date
)

REM Deactivate for now (we'll reactivate when starting the server)
call venv\Scripts\deactivate.bat
cd ..

REM Final system startup
echo.
echo [8/8] Starting application servers...
echo.

REM Start backend server with virtual environment activated
echo 🚀 Starting backend server (with virtual environment)...
start "Google Veo Backend Server" cmd /k "title Google Veo Backend && cd /d "%~dp0backend" && call venv\Scripts\activate.bat && echo ✅ Virtual environment activated && echo 🚀 Starting FastAPI server... && python main.py"

REM Wait a moment for backend to start
echo    Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

REM Start frontend development server
echo 🚀 Starting frontend development server...
start "Google Veo Frontend Server" cmd /k "title Google Veo Frontend && cd /d "%~dp0" && echo 🚀 Starting Vite development server... && npm run dev"

REM Final status and instructions
echo.
echo ========================================
echo ✅ Google Veo AI Video Generator Started
echo ========================================
echo.
echo 🌐 Application URLs:
echo    • Backend API: http://localhost:8000
echo    • Frontend UI:  http://localhost:3000
echo    • API Docs:    http://localhost:8000/docs
echo.
echo 📋 Server Status:
echo    • Backend: Starting with Python virtual environment
echo    • Frontend: Starting with Vite development server
echo.
echo 💡 Next Steps:
echo    1. Wait for both servers to fully start (check the opened windows)
echo    2. Open http://localhost:3000 in your web browser
echo    3. Configure your Google Cloud credentials in .env if not done already
echo    4. Start generating amazing videos with Google Veo!
echo.
echo 🔧 Troubleshooting:
echo    • If servers fail to start, check the opened terminal windows for errors
echo    • Ensure your .env file is properly configured
echo    • Run 'python setup-check.py' to verify your setup
echo.
echo ⚠️  Keep this window open - closing it won't stop the servers
echo    To stop servers: Close the individual server windows or press Ctrl+C in them
echo.
echo Press any key to exit this startup script...
pause >nul
