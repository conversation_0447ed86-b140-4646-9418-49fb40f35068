# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Code generated by the Google Gen AI SDK generator DO NOT EDIT.

from typing import Any, Optional, Union
from . import _transformers as t
from ._api_client import BaseApiClient
from ._common import get_value_by_path as getv
from ._common import set_value_by_path as setv


def _PrebuiltVoiceConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['voice_name']) is not None:
    setv(to_object, ['voiceName'], getv(from_object, ['voice_name']))

  return to_object


def _PrebuiltVoiceConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['voice_name']) is not None:
    setv(to_object, ['voiceName'], getv(from_object, ['voice_name']))

  return to_object


def _VoiceConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['prebuilt_voice_config']) is not None:
    setv(
        to_object,
        ['prebuiltVoiceConfig'],
        _PrebuiltVoiceConfig_to_mldev(
            api_client, getv(from_object, ['prebuilt_voice_config']), to_object
        ),
    )

  return to_object


def _VoiceConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['prebuilt_voice_config']) is not None:
    setv(
        to_object,
        ['prebuiltVoiceConfig'],
        _PrebuiltVoiceConfig_to_vertex(
            api_client, getv(from_object, ['prebuilt_voice_config']), to_object
        ),
    )

  return to_object


def _SpeakerVoiceConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['speaker']) is not None:
    setv(to_object, ['speaker'], getv(from_object, ['speaker']))

  if getv(from_object, ['voice_config']) is not None:
    setv(
        to_object,
        ['voiceConfig'],
        _VoiceConfig_to_mldev(
            api_client, getv(from_object, ['voice_config']), to_object
        ),
    )

  return to_object


def _SpeakerVoiceConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['speaker']) is not None:
    raise ValueError('speaker parameter is not supported in Vertex AI.')

  if getv(from_object, ['voice_config']) is not None:
    raise ValueError('voice_config parameter is not supported in Vertex AI.')

  return to_object


def _MultiSpeakerVoiceConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['speaker_voice_configs']) is not None:
    setv(
        to_object,
        ['speakerVoiceConfigs'],
        [
            _SpeakerVoiceConfig_to_mldev(api_client, item, to_object)
            for item in getv(from_object, ['speaker_voice_configs'])
        ],
    )

  return to_object


def _MultiSpeakerVoiceConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['speaker_voice_configs']) is not None:
    raise ValueError(
        'speaker_voice_configs parameter is not supported in Vertex AI.'
    )

  return to_object


def _SpeechConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['voice_config']) is not None:
    setv(
        to_object,
        ['voiceConfig'],
        _VoiceConfig_to_mldev(
            api_client, getv(from_object, ['voice_config']), to_object
        ),
    )

  if getv(from_object, ['multi_speaker_voice_config']) is not None:
    setv(
        to_object,
        ['multiSpeakerVoiceConfig'],
        _MultiSpeakerVoiceConfig_to_mldev(
            api_client,
            getv(from_object, ['multi_speaker_voice_config']),
            to_object,
        ),
    )

  if getv(from_object, ['language_code']) is not None:
    setv(to_object, ['languageCode'], getv(from_object, ['language_code']))

  return to_object


def _SpeechConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['voice_config']) is not None:
    setv(
        to_object,
        ['voiceConfig'],
        _VoiceConfig_to_vertex(
            api_client, getv(from_object, ['voice_config']), to_object
        ),
    )

  if getv(from_object, ['multi_speaker_voice_config']) is not None:
    raise ValueError(
        'multi_speaker_voice_config parameter is not supported in Vertex AI.'
    )

  if getv(from_object, ['language_code']) is not None:
    setv(to_object, ['languageCode'], getv(from_object, ['language_code']))

  return to_object


def _VideoMetadata_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['fps']) is not None:
    setv(to_object, ['fps'], getv(from_object, ['fps']))

  if getv(from_object, ['end_offset']) is not None:
    setv(to_object, ['endOffset'], getv(from_object, ['end_offset']))

  if getv(from_object, ['start_offset']) is not None:
    setv(to_object, ['startOffset'], getv(from_object, ['start_offset']))

  return to_object


def _VideoMetadata_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['fps']) is not None:
    setv(to_object, ['fps'], getv(from_object, ['fps']))

  if getv(from_object, ['end_offset']) is not None:
    setv(to_object, ['endOffset'], getv(from_object, ['end_offset']))

  if getv(from_object, ['start_offset']) is not None:
    setv(to_object, ['startOffset'], getv(from_object, ['start_offset']))

  return to_object


def _Blob_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['display_name']) is not None:
    raise ValueError('display_name parameter is not supported in Gemini API.')

  if getv(from_object, ['data']) is not None:
    setv(to_object, ['data'], getv(from_object, ['data']))

  if getv(from_object, ['mime_type']) is not None:
    setv(to_object, ['mimeType'], getv(from_object, ['mime_type']))

  return to_object


def _Blob_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['display_name']) is not None:
    setv(to_object, ['displayName'], getv(from_object, ['display_name']))

  if getv(from_object, ['data']) is not None:
    setv(to_object, ['data'], getv(from_object, ['data']))

  if getv(from_object, ['mime_type']) is not None:
    setv(to_object, ['mimeType'], getv(from_object, ['mime_type']))

  return to_object


def _FileData_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['display_name']) is not None:
    raise ValueError('display_name parameter is not supported in Gemini API.')

  if getv(from_object, ['file_uri']) is not None:
    setv(to_object, ['fileUri'], getv(from_object, ['file_uri']))

  if getv(from_object, ['mime_type']) is not None:
    setv(to_object, ['mimeType'], getv(from_object, ['mime_type']))

  return to_object


def _FileData_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['display_name']) is not None:
    setv(to_object, ['displayName'], getv(from_object, ['display_name']))

  if getv(from_object, ['file_uri']) is not None:
    setv(to_object, ['fileUri'], getv(from_object, ['file_uri']))

  if getv(from_object, ['mime_type']) is not None:
    setv(to_object, ['mimeType'], getv(from_object, ['mime_type']))

  return to_object


def _Part_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['video_metadata']) is not None:
    setv(
        to_object,
        ['videoMetadata'],
        _VideoMetadata_to_mldev(
            api_client, getv(from_object, ['video_metadata']), to_object
        ),
    )

  if getv(from_object, ['thought']) is not None:
    setv(to_object, ['thought'], getv(from_object, ['thought']))

  if getv(from_object, ['inline_data']) is not None:
    setv(
        to_object,
        ['inlineData'],
        _Blob_to_mldev(
            api_client, getv(from_object, ['inline_data']), to_object
        ),
    )

  if getv(from_object, ['file_data']) is not None:
    setv(
        to_object,
        ['fileData'],
        _FileData_to_mldev(
            api_client, getv(from_object, ['file_data']), to_object
        ),
    )

  if getv(from_object, ['code_execution_result']) is not None:
    setv(
        to_object,
        ['codeExecutionResult'],
        getv(from_object, ['code_execution_result']),
    )

  if getv(from_object, ['executable_code']) is not None:
    setv(to_object, ['executableCode'], getv(from_object, ['executable_code']))

  if getv(from_object, ['function_call']) is not None:
    setv(to_object, ['functionCall'], getv(from_object, ['function_call']))

  if getv(from_object, ['function_response']) is not None:
    setv(
        to_object,
        ['functionResponse'],
        getv(from_object, ['function_response']),
    )

  if getv(from_object, ['text']) is not None:
    setv(to_object, ['text'], getv(from_object, ['text']))

  return to_object


def _Part_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['video_metadata']) is not None:
    setv(
        to_object,
        ['videoMetadata'],
        _VideoMetadata_to_vertex(
            api_client, getv(from_object, ['video_metadata']), to_object
        ),
    )

  if getv(from_object, ['thought']) is not None:
    setv(to_object, ['thought'], getv(from_object, ['thought']))

  if getv(from_object, ['inline_data']) is not None:
    setv(
        to_object,
        ['inlineData'],
        _Blob_to_vertex(
            api_client, getv(from_object, ['inline_data']), to_object
        ),
    )

  if getv(from_object, ['file_data']) is not None:
    setv(
        to_object,
        ['fileData'],
        _FileData_to_vertex(
            api_client, getv(from_object, ['file_data']), to_object
        ),
    )

  if getv(from_object, ['code_execution_result']) is not None:
    setv(
        to_object,
        ['codeExecutionResult'],
        getv(from_object, ['code_execution_result']),
    )

  if getv(from_object, ['executable_code']) is not None:
    setv(to_object, ['executableCode'], getv(from_object, ['executable_code']))

  if getv(from_object, ['function_call']) is not None:
    setv(to_object, ['functionCall'], getv(from_object, ['function_call']))

  if getv(from_object, ['function_response']) is not None:
    setv(
        to_object,
        ['functionResponse'],
        getv(from_object, ['function_response']),
    )

  if getv(from_object, ['text']) is not None:
    setv(to_object, ['text'], getv(from_object, ['text']))

  return to_object


def _Content_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['parts']) is not None:
    setv(
        to_object,
        ['parts'],
        [
            _Part_to_mldev(api_client, item, to_object)
            for item in getv(from_object, ['parts'])
        ],
    )

  if getv(from_object, ['role']) is not None:
    setv(to_object, ['role'], getv(from_object, ['role']))

  return to_object


def _Content_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['parts']) is not None:
    setv(
        to_object,
        ['parts'],
        [
            _Part_to_vertex(api_client, item, to_object)
            for item in getv(from_object, ['parts'])
        ],
    )

  if getv(from_object, ['role']) is not None:
    setv(to_object, ['role'], getv(from_object, ['role']))

  return to_object


def _FunctionDeclaration_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['behavior']) is not None:
    setv(to_object, ['behavior'], getv(from_object, ['behavior']))

  if getv(from_object, ['description']) is not None:
    setv(to_object, ['description'], getv(from_object, ['description']))

  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  if getv(from_object, ['parameters']) is not None:
    setv(to_object, ['parameters'], getv(from_object, ['parameters']))

  if getv(from_object, ['response']) is not None:
    setv(to_object, ['response'], getv(from_object, ['response']))

  return to_object


def _FunctionDeclaration_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['behavior']) is not None:
    raise ValueError('behavior parameter is not supported in Vertex AI.')

  if getv(from_object, ['description']) is not None:
    setv(to_object, ['description'], getv(from_object, ['description']))

  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  if getv(from_object, ['parameters']) is not None:
    setv(to_object, ['parameters'], getv(from_object, ['parameters']))

  if getv(from_object, ['response']) is not None:
    setv(to_object, ['response'], getv(from_object, ['response']))

  return to_object


def _Interval_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['start_time']) is not None:
    setv(to_object, ['startTime'], getv(from_object, ['start_time']))

  if getv(from_object, ['end_time']) is not None:
    setv(to_object, ['endTime'], getv(from_object, ['end_time']))

  return to_object


def _Interval_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['start_time']) is not None:
    setv(to_object, ['startTime'], getv(from_object, ['start_time']))

  if getv(from_object, ['end_time']) is not None:
    setv(to_object, ['endTime'], getv(from_object, ['end_time']))

  return to_object


def _GoogleSearch_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['time_range_filter']) is not None:
    setv(
        to_object,
        ['timeRangeFilter'],
        _Interval_to_mldev(
            api_client, getv(from_object, ['time_range_filter']), to_object
        ),
    )

  return to_object


def _GoogleSearch_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['time_range_filter']) is not None:
    setv(
        to_object,
        ['timeRangeFilter'],
        _Interval_to_vertex(
            api_client, getv(from_object, ['time_range_filter']), to_object
        ),
    )

  return to_object


def _DynamicRetrievalConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['mode']) is not None:
    setv(to_object, ['mode'], getv(from_object, ['mode']))

  if getv(from_object, ['dynamic_threshold']) is not None:
    setv(
        to_object,
        ['dynamicThreshold'],
        getv(from_object, ['dynamic_threshold']),
    )

  return to_object


def _DynamicRetrievalConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['mode']) is not None:
    setv(to_object, ['mode'], getv(from_object, ['mode']))

  if getv(from_object, ['dynamic_threshold']) is not None:
    setv(
        to_object,
        ['dynamicThreshold'],
        getv(from_object, ['dynamic_threshold']),
    )

  return to_object


def _GoogleSearchRetrieval_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['dynamic_retrieval_config']) is not None:
    setv(
        to_object,
        ['dynamicRetrievalConfig'],
        _DynamicRetrievalConfig_to_mldev(
            api_client,
            getv(from_object, ['dynamic_retrieval_config']),
            to_object,
        ),
    )

  return to_object


def _GoogleSearchRetrieval_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['dynamic_retrieval_config']) is not None:
    setv(
        to_object,
        ['dynamicRetrievalConfig'],
        _DynamicRetrievalConfig_to_vertex(
            api_client,
            getv(from_object, ['dynamic_retrieval_config']),
            to_object,
        ),
    )

  return to_object


def _EnterpriseWebSearch_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _EnterpriseWebSearch_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _ApiKeyConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['api_key_string']) is not None:
    raise ValueError('api_key_string parameter is not supported in Gemini API.')

  return to_object


def _ApiKeyConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['api_key_string']) is not None:
    setv(to_object, ['apiKeyString'], getv(from_object, ['api_key_string']))

  return to_object


def _AuthConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['api_key_config']) is not None:
    raise ValueError('api_key_config parameter is not supported in Gemini API.')

  if getv(from_object, ['auth_type']) is not None:
    setv(to_object, ['authType'], getv(from_object, ['auth_type']))

  if getv(from_object, ['google_service_account_config']) is not None:
    setv(
        to_object,
        ['googleServiceAccountConfig'],
        getv(from_object, ['google_service_account_config']),
    )

  if getv(from_object, ['http_basic_auth_config']) is not None:
    setv(
        to_object,
        ['httpBasicAuthConfig'],
        getv(from_object, ['http_basic_auth_config']),
    )

  if getv(from_object, ['oauth_config']) is not None:
    setv(to_object, ['oauthConfig'], getv(from_object, ['oauth_config']))

  if getv(from_object, ['oidc_config']) is not None:
    setv(to_object, ['oidcConfig'], getv(from_object, ['oidc_config']))

  return to_object


def _AuthConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['api_key_config']) is not None:
    setv(
        to_object,
        ['apiKeyConfig'],
        _ApiKeyConfig_to_vertex(
            api_client, getv(from_object, ['api_key_config']), to_object
        ),
    )

  if getv(from_object, ['auth_type']) is not None:
    setv(to_object, ['authType'], getv(from_object, ['auth_type']))

  if getv(from_object, ['google_service_account_config']) is not None:
    setv(
        to_object,
        ['googleServiceAccountConfig'],
        getv(from_object, ['google_service_account_config']),
    )

  if getv(from_object, ['http_basic_auth_config']) is not None:
    setv(
        to_object,
        ['httpBasicAuthConfig'],
        getv(from_object, ['http_basic_auth_config']),
    )

  if getv(from_object, ['oauth_config']) is not None:
    setv(to_object, ['oauthConfig'], getv(from_object, ['oauth_config']))

  if getv(from_object, ['oidc_config']) is not None:
    setv(to_object, ['oidcConfig'], getv(from_object, ['oidc_config']))

  return to_object


def _GoogleMaps_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['auth_config']) is not None:
    raise ValueError('auth_config parameter is not supported in Gemini API.')

  return to_object


def _GoogleMaps_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['auth_config']) is not None:
    setv(
        to_object,
        ['authConfig'],
        _AuthConfig_to_vertex(
            api_client, getv(from_object, ['auth_config']), to_object
        ),
    )

  return to_object


def _UrlContext_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _UrlContext_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _Tool_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['function_declarations']) is not None:
    setv(
        to_object,
        ['functionDeclarations'],
        [
            _FunctionDeclaration_to_mldev(api_client, item, to_object)
            for item in getv(from_object, ['function_declarations'])
        ],
    )

  if getv(from_object, ['retrieval']) is not None:
    raise ValueError('retrieval parameter is not supported in Gemini API.')

  if getv(from_object, ['google_search']) is not None:
    setv(
        to_object,
        ['googleSearch'],
        _GoogleSearch_to_mldev(
            api_client, getv(from_object, ['google_search']), to_object
        ),
    )

  if getv(from_object, ['google_search_retrieval']) is not None:
    setv(
        to_object,
        ['googleSearchRetrieval'],
        _GoogleSearchRetrieval_to_mldev(
            api_client,
            getv(from_object, ['google_search_retrieval']),
            to_object,
        ),
    )

  if getv(from_object, ['enterprise_web_search']) is not None:
    raise ValueError(
        'enterprise_web_search parameter is not supported in Gemini API.'
    )

  if getv(from_object, ['google_maps']) is not None:
    raise ValueError('google_maps parameter is not supported in Gemini API.')

  if getv(from_object, ['url_context']) is not None:
    setv(
        to_object,
        ['urlContext'],
        _UrlContext_to_mldev(
            api_client, getv(from_object, ['url_context']), to_object
        ),
    )

  if getv(from_object, ['code_execution']) is not None:
    setv(to_object, ['codeExecution'], getv(from_object, ['code_execution']))

  return to_object


def _Tool_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['function_declarations']) is not None:
    setv(
        to_object,
        ['functionDeclarations'],
        [
            _FunctionDeclaration_to_vertex(api_client, item, to_object)
            for item in getv(from_object, ['function_declarations'])
        ],
    )

  if getv(from_object, ['retrieval']) is not None:
    setv(to_object, ['retrieval'], getv(from_object, ['retrieval']))

  if getv(from_object, ['google_search']) is not None:
    setv(
        to_object,
        ['googleSearch'],
        _GoogleSearch_to_vertex(
            api_client, getv(from_object, ['google_search']), to_object
        ),
    )

  if getv(from_object, ['google_search_retrieval']) is not None:
    setv(
        to_object,
        ['googleSearchRetrieval'],
        _GoogleSearchRetrieval_to_vertex(
            api_client,
            getv(from_object, ['google_search_retrieval']),
            to_object,
        ),
    )

  if getv(from_object, ['enterprise_web_search']) is not None:
    setv(
        to_object,
        ['enterpriseWebSearch'],
        _EnterpriseWebSearch_to_vertex(
            api_client, getv(from_object, ['enterprise_web_search']), to_object
        ),
    )

  if getv(from_object, ['google_maps']) is not None:
    setv(
        to_object,
        ['googleMaps'],
        _GoogleMaps_to_vertex(
            api_client, getv(from_object, ['google_maps']), to_object
        ),
    )

  if getv(from_object, ['url_context']) is not None:
    raise ValueError('url_context parameter is not supported in Vertex AI.')

  if getv(from_object, ['code_execution']) is not None:
    setv(to_object, ['codeExecution'], getv(from_object, ['code_execution']))

  return to_object


def _SessionResumptionConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['handle']) is not None:
    setv(to_object, ['handle'], getv(from_object, ['handle']))

  if getv(from_object, ['transparent']) is not None:
    raise ValueError('transparent parameter is not supported in Gemini API.')

  return to_object


def _SessionResumptionConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['handle']) is not None:
    setv(to_object, ['handle'], getv(from_object, ['handle']))

  if getv(from_object, ['transparent']) is not None:
    setv(to_object, ['transparent'], getv(from_object, ['transparent']))

  return to_object


def _AudioTranscriptionConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _AudioTranscriptionConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _AutomaticActivityDetection_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['disabled']) is not None:
    setv(to_object, ['disabled'], getv(from_object, ['disabled']))

  if getv(from_object, ['start_of_speech_sensitivity']) is not None:
    setv(
        to_object,
        ['startOfSpeechSensitivity'],
        getv(from_object, ['start_of_speech_sensitivity']),
    )

  if getv(from_object, ['end_of_speech_sensitivity']) is not None:
    setv(
        to_object,
        ['endOfSpeechSensitivity'],
        getv(from_object, ['end_of_speech_sensitivity']),
    )

  if getv(from_object, ['prefix_padding_ms']) is not None:
    setv(
        to_object, ['prefixPaddingMs'], getv(from_object, ['prefix_padding_ms'])
    )

  if getv(from_object, ['silence_duration_ms']) is not None:
    setv(
        to_object,
        ['silenceDurationMs'],
        getv(from_object, ['silence_duration_ms']),
    )

  return to_object


def _AutomaticActivityDetection_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['disabled']) is not None:
    setv(to_object, ['disabled'], getv(from_object, ['disabled']))

  if getv(from_object, ['start_of_speech_sensitivity']) is not None:
    setv(
        to_object,
        ['startOfSpeechSensitivity'],
        getv(from_object, ['start_of_speech_sensitivity']),
    )

  if getv(from_object, ['end_of_speech_sensitivity']) is not None:
    setv(
        to_object,
        ['endOfSpeechSensitivity'],
        getv(from_object, ['end_of_speech_sensitivity']),
    )

  if getv(from_object, ['prefix_padding_ms']) is not None:
    setv(
        to_object, ['prefixPaddingMs'], getv(from_object, ['prefix_padding_ms'])
    )

  if getv(from_object, ['silence_duration_ms']) is not None:
    setv(
        to_object,
        ['silenceDurationMs'],
        getv(from_object, ['silence_duration_ms']),
    )

  return to_object


def _RealtimeInputConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['automatic_activity_detection']) is not None:
    setv(
        to_object,
        ['automaticActivityDetection'],
        _AutomaticActivityDetection_to_mldev(
            api_client,
            getv(from_object, ['automatic_activity_detection']),
            to_object,
        ),
    )

  if getv(from_object, ['activity_handling']) is not None:
    setv(
        to_object,
        ['activityHandling'],
        getv(from_object, ['activity_handling']),
    )

  if getv(from_object, ['turn_coverage']) is not None:
    setv(to_object, ['turnCoverage'], getv(from_object, ['turn_coverage']))

  return to_object


def _RealtimeInputConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['automatic_activity_detection']) is not None:
    setv(
        to_object,
        ['automaticActivityDetection'],
        _AutomaticActivityDetection_to_vertex(
            api_client,
            getv(from_object, ['automatic_activity_detection']),
            to_object,
        ),
    )

  if getv(from_object, ['activity_handling']) is not None:
    setv(
        to_object,
        ['activityHandling'],
        getv(from_object, ['activity_handling']),
    )

  if getv(from_object, ['turn_coverage']) is not None:
    setv(to_object, ['turnCoverage'], getv(from_object, ['turn_coverage']))

  return to_object


def _SlidingWindow_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['target_tokens']) is not None:
    setv(to_object, ['targetTokens'], getv(from_object, ['target_tokens']))

  return to_object


def _SlidingWindow_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['target_tokens']) is not None:
    setv(to_object, ['targetTokens'], getv(from_object, ['target_tokens']))

  return to_object


def _ContextWindowCompressionConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['trigger_tokens']) is not None:
    setv(to_object, ['triggerTokens'], getv(from_object, ['trigger_tokens']))

  if getv(from_object, ['sliding_window']) is not None:
    setv(
        to_object,
        ['slidingWindow'],
        _SlidingWindow_to_mldev(
            api_client, getv(from_object, ['sliding_window']), to_object
        ),
    )

  return to_object


def _ContextWindowCompressionConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['trigger_tokens']) is not None:
    setv(to_object, ['triggerTokens'], getv(from_object, ['trigger_tokens']))

  if getv(from_object, ['sliding_window']) is not None:
    setv(
        to_object,
        ['slidingWindow'],
        _SlidingWindow_to_vertex(
            api_client, getv(from_object, ['sliding_window']), to_object
        ),
    )

  return to_object


def _ProactivityConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['proactive_audio']) is not None:
    setv(to_object, ['proactiveAudio'], getv(from_object, ['proactive_audio']))

  return to_object


def _ProactivityConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['proactive_audio']) is not None:
    setv(to_object, ['proactiveAudio'], getv(from_object, ['proactive_audio']))

  return to_object


def _LiveConnectConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['generation_config']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig'],
        getv(from_object, ['generation_config']),
    )

  if getv(from_object, ['response_modalities']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'responseModalities'],
        getv(from_object, ['response_modalities']),
    )

  if getv(from_object, ['temperature']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'temperature'],
        getv(from_object, ['temperature']),
    )

  if getv(from_object, ['top_p']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'topP'],
        getv(from_object, ['top_p']),
    )

  if getv(from_object, ['top_k']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'topK'],
        getv(from_object, ['top_k']),
    )

  if getv(from_object, ['max_output_tokens']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'maxOutputTokens'],
        getv(from_object, ['max_output_tokens']),
    )

  if getv(from_object, ['media_resolution']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'mediaResolution'],
        getv(from_object, ['media_resolution']),
    )

  if getv(from_object, ['seed']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'seed'],
        getv(from_object, ['seed']),
    )

  if getv(from_object, ['speech_config']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'speechConfig'],
        _SpeechConfig_to_mldev(
            api_client,
            t.t_live_speech_config(
                api_client, getv(from_object, ['speech_config'])
            ),
            to_object,
        ),
    )

  if getv(from_object, ['enable_affective_dialog']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'enableAffectiveDialog'],
        getv(from_object, ['enable_affective_dialog']),
    )

  if getv(from_object, ['system_instruction']) is not None:
    setv(
        parent_object,
        ['setup', 'systemInstruction'],
        _Content_to_mldev(
            api_client,
            t.t_content(api_client, getv(from_object, ['system_instruction'])),
            to_object,
        ),
    )

  if getv(from_object, ['tools']) is not None:
    setv(
        parent_object,
        ['setup', 'tools'],
        [
            _Tool_to_mldev(api_client, t.t_tool(api_client, item), to_object)
            for item in t.t_tools(api_client, getv(from_object, ['tools']))
        ],
    )

  if getv(from_object, ['session_resumption']) is not None:
    setv(
        parent_object,
        ['setup', 'sessionResumption'],
        _SessionResumptionConfig_to_mldev(
            api_client, getv(from_object, ['session_resumption']), to_object
        ),
    )

  if getv(from_object, ['input_audio_transcription']) is not None:
    setv(
        parent_object,
        ['setup', 'inputAudioTranscription'],
        _AudioTranscriptionConfig_to_mldev(
            api_client,
            getv(from_object, ['input_audio_transcription']),
            to_object,
        ),
    )

  if getv(from_object, ['output_audio_transcription']) is not None:
    setv(
        parent_object,
        ['setup', 'outputAudioTranscription'],
        _AudioTranscriptionConfig_to_mldev(
            api_client,
            getv(from_object, ['output_audio_transcription']),
            to_object,
        ),
    )

  if getv(from_object, ['realtime_input_config']) is not None:
    setv(
        parent_object,
        ['setup', 'realtimeInputConfig'],
        _RealtimeInputConfig_to_mldev(
            api_client, getv(from_object, ['realtime_input_config']), to_object
        ),
    )

  if getv(from_object, ['context_window_compression']) is not None:
    setv(
        parent_object,
        ['setup', 'contextWindowCompression'],
        _ContextWindowCompressionConfig_to_mldev(
            api_client,
            getv(from_object, ['context_window_compression']),
            to_object,
        ),
    )

  if getv(from_object, ['proactivity']) is not None:
    setv(
        parent_object,
        ['setup', 'proactivity'],
        _ProactivityConfig_to_mldev(
            api_client, getv(from_object, ['proactivity']), to_object
        ),
    )

  return to_object


def _LiveConnectConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['generation_config']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig'],
        getv(from_object, ['generation_config']),
    )

  if getv(from_object, ['response_modalities']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'responseModalities'],
        getv(from_object, ['response_modalities']),
    )

  if getv(from_object, ['temperature']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'temperature'],
        getv(from_object, ['temperature']),
    )

  if getv(from_object, ['top_p']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'topP'],
        getv(from_object, ['top_p']),
    )

  if getv(from_object, ['top_k']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'topK'],
        getv(from_object, ['top_k']),
    )

  if getv(from_object, ['max_output_tokens']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'maxOutputTokens'],
        getv(from_object, ['max_output_tokens']),
    )

  if getv(from_object, ['media_resolution']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'mediaResolution'],
        getv(from_object, ['media_resolution']),
    )

  if getv(from_object, ['seed']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'seed'],
        getv(from_object, ['seed']),
    )

  if getv(from_object, ['speech_config']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'speechConfig'],
        _SpeechConfig_to_vertex(
            api_client,
            t.t_live_speech_config(
                api_client, getv(from_object, ['speech_config'])
            ),
            to_object,
        ),
    )

  if getv(from_object, ['enable_affective_dialog']) is not None:
    setv(
        parent_object,
        ['setup', 'generationConfig', 'enableAffectiveDialog'],
        getv(from_object, ['enable_affective_dialog']),
    )

  if getv(from_object, ['system_instruction']) is not None:
    setv(
        parent_object,
        ['setup', 'systemInstruction'],
        _Content_to_vertex(
            api_client,
            t.t_content(api_client, getv(from_object, ['system_instruction'])),
            to_object,
        ),
    )

  if getv(from_object, ['tools']) is not None:
    setv(
        parent_object,
        ['setup', 'tools'],
        [
            _Tool_to_vertex(api_client, t.t_tool(api_client, item), to_object)
            for item in t.t_tools(api_client, getv(from_object, ['tools']))
        ],
    )

  if getv(from_object, ['session_resumption']) is not None:
    setv(
        parent_object,
        ['setup', 'sessionResumption'],
        _SessionResumptionConfig_to_vertex(
            api_client, getv(from_object, ['session_resumption']), to_object
        ),
    )

  if getv(from_object, ['input_audio_transcription']) is not None:
    setv(
        parent_object,
        ['setup', 'inputAudioTranscription'],
        _AudioTranscriptionConfig_to_vertex(
            api_client,
            getv(from_object, ['input_audio_transcription']),
            to_object,
        ),
    )

  if getv(from_object, ['output_audio_transcription']) is not None:
    setv(
        parent_object,
        ['setup', 'outputAudioTranscription'],
        _AudioTranscriptionConfig_to_vertex(
            api_client,
            getv(from_object, ['output_audio_transcription']),
            to_object,
        ),
    )

  if getv(from_object, ['realtime_input_config']) is not None:
    setv(
        parent_object,
        ['setup', 'realtimeInputConfig'],
        _RealtimeInputConfig_to_vertex(
            api_client, getv(from_object, ['realtime_input_config']), to_object
        ),
    )

  if getv(from_object, ['context_window_compression']) is not None:
    setv(
        parent_object,
        ['setup', 'contextWindowCompression'],
        _ContextWindowCompressionConfig_to_vertex(
            api_client,
            getv(from_object, ['context_window_compression']),
            to_object,
        ),
    )

  if getv(from_object, ['proactivity']) is not None:
    setv(
        parent_object,
        ['setup', 'proactivity'],
        _ProactivityConfig_to_vertex(
            api_client, getv(from_object, ['proactivity']), to_object
        ),
    )

  return to_object


def _LiveEphemeralParameters_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['model']) is not None:
    setv(
        to_object,
        ['setup', 'model'],
        t.t_model(api_client, getv(from_object, ['model'])),
    )

  if getv(from_object, ['config']) is not None:
    setv(
        to_object,
        ['config'],
        _LiveConnectConfig_to_mldev(
            api_client, getv(from_object, ['config']), to_object
        ),
    )

  return to_object


def _LiveEphemeralParameters_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['model']) is not None:
    raise ValueError('model parameter is not supported in Vertex AI.')

  if getv(from_object, ['config']) is not None:
    raise ValueError('config parameter is not supported in Vertex AI.')

  return to_object


def _CreateAuthTokenConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['expire_time']) is not None:
    setv(parent_object, ['expireTime'], getv(from_object, ['expire_time']))

  if getv(from_object, ['new_session_expire_time']) is not None:
    setv(
        parent_object,
        ['newSessionExpireTime'],
        getv(from_object, ['new_session_expire_time']),
    )

  if getv(from_object, ['uses']) is not None:
    setv(parent_object, ['uses'], getv(from_object, ['uses']))

  if getv(from_object, ['live_ephemeral_parameters']) is not None:
    setv(
        parent_object,
        ['bidiGenerateContentSetup'],
        _LiveEphemeralParameters_to_mldev(
            api_client,
            getv(from_object, ['live_ephemeral_parameters']),
            to_object,
        ),
    )

  if getv(from_object, ['lock_additional_fields']) is not None:
    setv(
        parent_object,
        ['fieldMask'],
        getv(from_object, ['lock_additional_fields']),
    )

  return to_object


def _CreateAuthTokenConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['expire_time']) is not None:
    raise ValueError('expire_time parameter is not supported in Vertex AI.')

  if getv(from_object, ['new_session_expire_time']) is not None:
    raise ValueError(
        'new_session_expire_time parameter is not supported in Vertex AI.'
    )

  if getv(from_object, ['uses']) is not None:
    raise ValueError('uses parameter is not supported in Vertex AI.')

  if getv(from_object, ['live_ephemeral_parameters']) is not None:
    raise ValueError(
        'live_ephemeral_parameters parameter is not supported in Vertex AI.'
    )

  if getv(from_object, ['lock_additional_fields']) is not None:
    raise ValueError(
        'lock_additional_fields parameter is not supported in Vertex AI.'
    )

  return to_object


def _CreateAuthTokenParameters_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['config']) is not None:
    setv(
        to_object,
        ['config'],
        _CreateAuthTokenConfig_to_mldev(
            api_client, getv(from_object, ['config']), to_object
        ),
    )

  return to_object


def _CreateAuthTokenParameters_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['config']) is not None:
    raise ValueError('config parameter is not supported in Vertex AI.')

  return to_object


def _AuthToken_from_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  return to_object


def _AuthToken_from_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object
