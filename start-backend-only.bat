@echo off
setlocal enabledelayedexpansion
echo ========================================
echo Google Veo AI - Backend Only Startup
echo ========================================
echo.

echo 🔧 Starting backend server only (Node.js not required)
echo.

REM Check if Python is installed
echo [1/4] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Python is not installed or not in PATH
    echo    Please install Python from https://python.org/
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ✅ Python found: !PYTHON_VERSION!

REM Check virtual environment
echo.
echo [2/4] Checking virtual environment...
if not exist backend\venv (
    echo ❌ Error: Virtual environment not found
    echo    Please run the full start.bat first to create the environment
    echo.
    pause
    exit /b 1
)
echo ✅ Virtual environment found

REM Check dependencies
echo.
echo [3/4] Checking dependencies...
cd backend
call venv\Scripts\activate.bat
pip show fastapi >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Dependencies not installed
    echo    Please run the full start.bat first to install dependencies
    call venv\Scripts\deactivate.bat >nul 2>&1
    cd ..
    echo.
    pause
    exit /b 1
)
echo ✅ Dependencies verified

REM Start backend server
echo.
echo [4/4] Starting backend server...
echo.
echo 🚀 Starting FastAPI server on http://localhost:8000
echo    • Health Check: http://localhost:8000/api/health
echo    • API Docs: http://localhost:8000/docs
echo    • GenAI Test: http://localhost:8000/api/test/genai
echo.
echo 💡 Press Ctrl+C to stop the server
echo.

python main.py

echo.
echo Server stopped.
cd ..
echo.
echo Press any key to close...
pause >nul
