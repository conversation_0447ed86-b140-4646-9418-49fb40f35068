# Google API Configuration
# Note: API key should be set in Windows Environment Variables as "Google API"
# This will be automatically detected by the application
GOOGLE_API_KEY_ENV_NAME=Google API

# Google Cloud Configuration (for Vertex AI/Veo)
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json

# Google Cloud Storage (Optional - for storing generated videos)
GOOGLE_CLOUD_STORAGE_BUCKET=your-storage-bucket-name

# Veo Model Configuration
VEO_MODEL_ID=veo-2.0-generate-001

# Development Settings
NODE_ENV=development
VITE_API_BASE_URL=http://localhost:8000/api
