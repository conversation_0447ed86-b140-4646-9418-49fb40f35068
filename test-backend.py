#!/usr/bin/env python3
"""
Simple test script to verify the backend is working
"""

import requests
import json
import time
import sys

def test_health_endpoint():
    """Test the health check endpoint"""
    try:
        response = requests.get('http://localhost:8000/api/health', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Health check passed")
            print(f"   Status: {data.get('status')}")
            print(f"   Services: {data.get('services', {})}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server")
        print("   Make sure the backend is running on http://localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_root_endpoint():
    """Test the root endpoint"""
    try:
        response = requests.get('http://localhost:8000/', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Root endpoint working")
            print(f"   Message: {data.get('message')}")
            return True
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")
        return False

def test_videos_endpoint():
    """Test the videos list endpoint"""
    try:
        response = requests.get('http://localhost:8000/api/videos', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Videos endpoint working")
            print(f"   Videos count: {len(data.get('videos', []))}")
            return True
        else:
            print(f"❌ Videos endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Videos endpoint error: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Google Veo Backend API")
    print("=" * 40)
    
    print("\n🔍 Testing backend endpoints...")
    
    # Test basic connectivity
    root_ok = test_root_endpoint()
    
    if not root_ok:
        print("\n❌ Basic connectivity failed. Is the backend server running?")
        print("   Start the backend with: cd backend && python main.py")
        sys.exit(1)
    
    # Test health check
    health_ok = test_health_endpoint()
    
    # Test videos endpoint
    videos_ok = test_videos_endpoint()
    
    print("\n" + "=" * 40)
    if root_ok and health_ok and videos_ok:
        print("🎉 All backend tests passed!")
        print("\n✨ Backend is ready for video generation")
    else:
        print("⚠️  Some tests failed. Check the backend configuration.")
    
    print("\n📝 Next steps:")
    print("   1. Start the frontend: npm run dev")
    print("   2. Open http://localhost:3000 in your browser")
    print("   3. Try generating a video!")

if __name__ == "__main__":
    main()
