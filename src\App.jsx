import React, { useState } from 'react'
import { Video, Type, Image, Settings } from 'lucide-react'
import TextToVideo from './components/TextToVideo'
import ImageToVideo from './components/ImageToVideo'
import VideoGallery from './components/VideoGallery'

function App() {
  const [activeTab, setActiveTab] = useState('text-to-video')

  const tabs = [
    { id: 'text-to-video', label: 'Text to Video', icon: Type },
    { id: 'image-to-video', label: 'Image to Video', icon: Image },
    { id: 'gallery', label: 'Video Gallery', icon: Video },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className="bg-primary-600 p-2 rounded-lg">
                <Video className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Google Veo</h1>
                <p className="text-sm text-gray-500">AI Video Generator</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-gray-400" />
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'text-to-video' && <TextToVideo />}
        {activeTab === 'image-to-video' && <ImageToVideo />}
        {activeTab === 'gallery' && <VideoGallery />}
      </main>
    </div>
  )
}

export default App
