import React, { useState } from 'react'
import { Download, Play, Pause, Volume2, VolumeX, Maximize2, Clock, FileVideo } from 'lucide-react'
import toast from 'react-hot-toast'

const VideoPlayer = ({ video, title, prompt }) => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)

  const handlePlayPause = (videoElement) => {
    if (videoElement.paused) {
      videoElement.play()
      setIsPlaying(true)
    } else {
      videoElement.pause()
      setIsPlaying(false)
    }
  }

  const handleMuteToggle = (videoElement) => {
    videoElement.muted = !videoElement.muted
    setIsMuted(videoElement.muted)
  }

  const handleFullscreen = (videoElement) => {
    if (videoElement.requestFullscreen) {
      videoElement.requestFullscreen()
    } else if (videoElement.webkitRequestFullscreen) {
      videoElement.webkitRequestFullscreen()
    } else if (videoElement.msRequestFullscreen) {
      videoElement.msRequestFullscreen()
    }
  }

  const handleDownload = async () => {
    if (!video.uri) {
      toast.error('Video URL not available for download')
      return
    }

    setIsDownloading(true)
    try {
      // Create a temporary link to download the video
      const link = document.createElement('a')
      link.href = video.uri
      link.download = `veo-video-${Date.now()}.mp4`
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      toast.success('Video download started')
    } catch (error) {
      console.error('Download error:', error)
      toast.error('Failed to download video')
    } finally {
      setIsDownloading(false)
    }
  }

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="card">
      {/* Video Container */}
      <div className="relative bg-black rounded-lg overflow-hidden mb-4">
        <video
          className="w-full h-auto"
          controls
          preload="metadata"
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onVolumeChange={(e) => setIsMuted(e.target.muted)}
          poster={video.thumbnail}
        >
          <source src={video.uri} type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Custom Controls Overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
          <div className="flex items-center justify-between text-white">
            <div className="flex items-center space-x-2">
              <button
                onClick={(e) => handlePlayPause(e.target.closest('.card').querySelector('video'))}
                className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
              >
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </button>
              
              <button
                onClick={(e) => handleMuteToggle(e.target.closest('.card').querySelector('video'))}
                className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
              >
                {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
              </button>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={(e) => handleFullscreen(e.target.closest('.card').querySelector('video'))}
                className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
              >
                <Maximize2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Video Info */}
      <div className="space-y-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 mb-1">{title}</h3>
            {prompt && (
              <p className="text-sm text-gray-600 line-clamp-2">{prompt}</p>
            )}
          </div>
        </div>

        {/* Video Metadata */}
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <FileVideo className="h-4 w-4" />
              <span>{video.encoding || 'MP4'}</span>
            </div>
            {video.duration && (
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>{formatDuration(video.duration)}</span>
              </div>
            )}
          </div>

          {/* Download Button */}
          <button
            onClick={handleDownload}
            disabled={isDownloading || !video.uri}
            className="flex items-center space-x-1 px-3 py-1 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Download className="h-4 w-4" />
            <span>{isDownloading ? 'Downloading...' : 'Download'}</span>
          </button>
        </div>

        {/* Video URL for debugging */}
        {process.env.NODE_ENV === 'development' && video.uri && (
          <div className="text-xs text-gray-400 break-all">
            <strong>URL:</strong> {video.uri}
          </div>
        )}
      </div>
    </div>
  )
}

export default VideoPlayer
