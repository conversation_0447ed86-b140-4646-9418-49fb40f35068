import React, { useState } from 'react'
import { Play, Loader2, Wand2 } from 'lucide-react'
import toast from 'react-hot-toast'
import { videoAPI } from '../utils/api'
import VideoPlayer from './VideoPlayer'
import OperationStatus from './OperationStatus'

const TextToVideo = () => {
  const [formData, setFormData] = useState({
    prompt: '',
    negativePrompt: '',
    durationSeconds: 8,
    aspectRatio: '16:9',
    sampleCount: 1,
    enhancePrompt: true,
    personGeneration: 'allow_adult',
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [operationId, setOperationId] = useState(null)
  const [generatedVideos, setGeneratedVideos] = useState([])

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!formData.prompt.trim()) {
      toast.error('Please enter a prompt for video generation')
      return
    }

    setIsGenerating(true)
    setOperationId(null)
    setGeneratedVideos([])

    try {
      const response = await videoAPI.generateTextToVideo(formData)
      setOperationId(response.operation_id)
      toast.success('Video generation started! This may take a few minutes.')
    } catch (error) {
      console.error('Error generating video:', error)
      toast.error(error.response?.data?.detail || 'Failed to start video generation')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleOperationComplete = (videos) => {
    setGeneratedVideos(videos)
    setOperationId(null)
    toast.success(`Successfully generated ${videos.length} video(s)!`)
  }

  const handleOperationError = (error) => {
    setOperationId(null)
    toast.error(error)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Text to Video</h2>
        <p className="text-gray-600">Transform your text descriptions into stunning videos using Google Veo</p>
      </div>

      {/* Generation Form */}
      <div className="card max-w-4xl mx-auto">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Main Prompt */}
          <div>
            <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-2">
              Video Description *
            </label>
            <textarea
              id="prompt"
              name="prompt"
              value={formData.prompt}
              onChange={handleInputChange}
              placeholder="Describe the video you want to generate... (e.g., 'A fast-tracking shot through a bustling dystopian sprawl with bright neon signs, flying cars and mist, night, lens flare, volumetric lighting')"
              className="input-field h-32 resize-none"
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              Be descriptive and specific for better results
            </p>
          </div>

          {/* Negative Prompt */}
          <div>
            <label htmlFor="negativePrompt" className="block text-sm font-medium text-gray-700 mb-2">
              Negative Prompt (Optional)
            </label>
            <textarea
              id="negativePrompt"
              name="negativePrompt"
              value={formData.negativePrompt}
              onChange={handleInputChange}
              placeholder="Describe what you don't want in the video... (e.g., 'overhead lighting, bright colors, people, animals')"
              className="input-field h-20 resize-none"
            />
          </div>

          {/* Settings Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Duration */}
            <div>
              <label htmlFor="durationSeconds" className="block text-sm font-medium text-gray-700 mb-2">
                Duration (seconds)
              </label>
              <select
                id="durationSeconds"
                name="durationSeconds"
                value={formData.durationSeconds}
                onChange={handleInputChange}
                className="input-field"
              >
                <option value={5}>5 seconds</option>
                <option value={6}>6 seconds</option>
                <option value={7}>7 seconds</option>
                <option value={8}>8 seconds</option>
              </select>
            </div>

            {/* Aspect Ratio */}
            <div>
              <label htmlFor="aspectRatio" className="block text-sm font-medium text-gray-700 mb-2">
                Aspect Ratio
              </label>
              <select
                id="aspectRatio"
                name="aspectRatio"
                value={formData.aspectRatio}
                onChange={handleInputChange}
                className="input-field"
              >
                <option value="16:9">16:9 (Landscape)</option>
                <option value="9:16">9:16 (Portrait)</option>
              </select>
            </div>

            {/* Sample Count */}
            <div>
              <label htmlFor="sampleCount" className="block text-sm font-medium text-gray-700 mb-2">
                Number of Videos
              </label>
              <select
                id="sampleCount"
                name="sampleCount"
                value={formData.sampleCount}
                onChange={handleInputChange}
                className="input-field"
              >
                <option value={1}>1 video</option>
                <option value={2}>2 videos</option>
                <option value={3}>3 videos</option>
                <option value={4}>4 videos</option>
              </select>
            </div>
          </div>

          {/* Advanced Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Advanced Settings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Person Generation */}
              <div>
                <label htmlFor="personGeneration" className="block text-sm font-medium text-gray-700 mb-2">
                  Person Generation
                </label>
                <select
                  id="personGeneration"
                  name="personGeneration"
                  value={formData.personGeneration}
                  onChange={handleInputChange}
                  className="input-field"
                >
                  <option value="allow_adult">Allow Adults</option>
                  <option value="dont_allow">Don't Allow People</option>
                </select>
              </div>

              {/* Enhance Prompt */}
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="enhancePrompt"
                  name="enhancePrompt"
                  checked={formData.enhancePrompt}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="enhancePrompt" className="text-sm font-medium text-gray-700">
                  Enhance prompt with Gemini
                </label>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-center">
            <button
              type="submit"
              disabled={isGenerating || !formData.prompt.trim()}
              className="btn-primary flex items-center space-x-2 px-8 py-3 text-lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <Wand2 className="h-5 w-5" />
                  <span>Generate Video</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Operation Status */}
      {operationId && (
        <OperationStatus
          operationId={operationId}
          onComplete={handleOperationComplete}
          onError={handleOperationError}
        />
      )}

      {/* Generated Videos */}
      {generatedVideos.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-900 text-center">Generated Videos</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {generatedVideos.map((video, index) => (
              <VideoPlayer
                key={index}
                video={video}
                title={`Generated Video ${index + 1}`}
                prompt={formData.prompt}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default TextToVideo
