#!/usr/bin/env python3
"""
Setup verification script for Google Veo AI Video Generator
Checks if all required dependencies and configurations are in place
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_command(command, name):
    """Check if a command is available"""
    try:
        result = subprocess.run([command, '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip().split('\n')[0]
            print(f"✅ {name}: {version}")
            return True
        else:
            print(f"❌ {name}: Command failed")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        print(f"❌ {name}: Not found")
        return False

def check_file(filepath, name, required=True):
    """Check if a file exists"""
    if os.path.exists(filepath):
        print(f"✅ {name}: Found")
        return True
    else:
        status = "❌" if required else "⚠️"
        print(f"{status} {name}: Not found")
        return not required

def check_env_file():
    """Check environment configuration"""
    env_path = Path('.env')
    env_example_path = Path('.env.example')
    
    if not env_path.exists():
        if env_example_path.exists():
            print("⚠️  .env file not found, but .env.example exists")
            print("   Please copy .env.example to .env and configure it")
            return False
        else:
            print("❌ Neither .env nor .env.example found")
            return False
    
    # Check required environment variables
    required_vars = [
        'GOOGLE_CLOUD_PROJECT',
        'GOOGLE_APPLICATION_CREDENTIALS'
    ]
    
    missing_vars = []
    with open(env_path, 'r') as f:
        env_content = f.read()
        for var in required_vars:
            if f"{var}=" not in env_content or f"{var}=your-" in env_content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  .env file exists but missing/unconfigured: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ .env file: Configured")
        return True

def check_google_cloud_setup():
    """Check Google Cloud setup"""
    print("\n🔍 Checking Google Cloud setup...")
    
    # Check if gcloud is installed
    gcloud_available = check_command('gcloud', 'Google Cloud SDK')
    
    if gcloud_available:
        # Check if authenticated
        try:
            result = subprocess.run(['gcloud', 'auth', 'list', '--format=json'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                accounts = json.loads(result.stdout)
                active_accounts = [acc for acc in accounts if acc.get('status') == 'ACTIVE']
                if active_accounts:
                    print(f"✅ Google Cloud Authentication: {active_accounts[0]['account']}")
                else:
                    print("⚠️  Google Cloud Authentication: No active account")
                    print("   Run: gcloud auth login")
            else:
                print("⚠️  Google Cloud Authentication: Unable to check")
        except Exception as e:
            print(f"⚠️  Google Cloud Authentication: Error checking ({e})")
    
    # Check service account key
    env_path = Path('.env')
    if env_path.exists():
        with open(env_path, 'r') as f:
            for line in f:
                if line.startswith('GOOGLE_APPLICATION_CREDENTIALS='):
                    cred_path = line.split('=', 1)[1].strip()
                    if cred_path and cred_path != 'path/to/your/service-account-key.json':
                        if os.path.exists(cred_path):
                            print(f"✅ Service Account Key: Found at {cred_path}")
                        else:
                            print(f"❌ Service Account Key: Not found at {cred_path}")
                    break

def main():
    """Main setup check function"""
    print("🚀 Google Veo AI Video Generator - Setup Check")
    print("=" * 50)
    
    all_good = True
    
    # Check system requirements
    print("\n🔍 Checking system requirements...")
    all_good &= check_command('node', 'Node.js')
    all_good &= check_command('npm', 'npm')
    
    # Check Python
    python_cmd = 'python3' if sys.platform != 'win32' else 'python'
    all_good &= check_command(python_cmd, 'Python')
    
    # Check project files
    print("\n🔍 Checking project files...")
    all_good &= check_file('package.json', 'package.json')
    all_good &= check_file('backend/requirements.txt', 'Backend requirements.txt')
    all_good &= check_file('backend/main.py', 'Backend main.py')
    
    # Check environment configuration
    print("\n🔍 Checking environment configuration...")
    env_ok = check_env_file()
    all_good &= env_ok
    
    # Check optional files
    print("\n🔍 Checking optional files...")
    check_file('node_modules', 'Frontend dependencies (node_modules)', required=False)
    
    # Check Google Cloud setup
    check_google_cloud_setup()
    
    # Summary
    print("\n" + "=" * 50)
    if all_good and env_ok:
        print("🎉 Setup looks good! You can run the application with:")
        if sys.platform == 'win32':
            print("   start.bat")
        else:
            print("   ./start.sh")
    else:
        print("⚠️  Setup incomplete. Please address the issues above.")
        print("\n📖 For detailed setup instructions, see README.md")
    
    print("\n🔗 Useful links:")
    print("   • Google Cloud Console: https://console.cloud.google.com/")
    print("   • Vertex AI: https://console.cloud.google.com/vertex-ai/")
    print("   • Google AI Studio: https://aistudio.google.com/")

if __name__ == "__main__":
    main()
