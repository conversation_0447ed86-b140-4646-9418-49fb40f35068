@echo off
echo Testing virtual environment activation...
echo.

cd backend
echo Current directory: %CD%
echo.

echo Activating virtual environment...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ Failed to activate virtual environment
    cd ..
    pause
    exit /b 1
)

echo ✅ Virtual environment activated successfully
echo.

echo Testing Python in virtual environment...
python --version
echo.

echo Testing pip in virtual environment...
pip --version
echo.

echo Testing package availability...
pip show requests
echo.

echo ✅ All tests passed!
echo.

call venv\Scripts\deactivate.bat
cd ..
echo ✅ Virtual environment deactivated
pause
