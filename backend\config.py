"""
Configuration module for Google Veo AI Video Generator
Handles environment variables and API key management
"""

import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for the application"""
    
    def __init__(self):
        # Google API Key (from Windows environment variable "Google API")
        self.google_api_key = self._get_google_api_key()
        
        # Google Cloud Configuration
        self.google_cloud_project = os.getenv('GOOGLE_CLOUD_PROJECT')
        self.google_cloud_location = os.getenv('GOOGLE_CLOUD_LOCATION', 'us-central1')
        self.google_application_credentials = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        
        # Google Cloud Storage
        self.google_cloud_storage_bucket = os.getenv('GOOGLE_CLOUD_STORAGE_BUCKET')
        
        # Veo Model Configuration
        self.veo_model_id = os.getenv('VEO_MODEL_ID', 'veo-2.0-generate-001')
        
        # Development Settings
        self.node_env = os.getenv('NODE_ENV', 'development')
        self.api_base_url = os.getenv('VITE_API_BASE_URL', 'http://localhost:8000/api')
    
    def _get_google_api_key(self) -> Optional[str]:
        """
        Get Google API key from Windows environment variable
        Tries multiple possible environment variable names
        """
        # Primary: User's specified environment variable name
        api_key_env_name = os.getenv('GOOGLE_API_KEY_ENV_NAME', 'Google API')
        api_key = os.getenv(api_key_env_name)
        
        if api_key:
            return api_key
        
        # Fallback options
        fallback_names = [
            'GOOGLE_API_KEY',
            'GEMINI_API_KEY',
            'GOOGLE_AI_API_KEY',
            'Google API',  # User's specified name
            'GoogleAPI',
            'Google_API'
        ]
        
        for name in fallback_names:
            api_key = os.getenv(name)
            if api_key:
                return api_key
        
        return None
    
    def validate_configuration(self) -> dict:
        """
        Validate the configuration and return status
        """
        status = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'info': []
        }
        
        # Check Google API Key
        if not self.google_api_key:
            status['errors'].append(
                'Google API key not found. Please set "Google API" environment variable.'
            )
            status['valid'] = False
        else:
            status['info'].append('Google API key found')
        
        # Check Google Cloud Project (optional for some features)
        if not self.google_cloud_project:
            status['warnings'].append(
                'Google Cloud Project not set. Some features may not work.'
            )
        else:
            status['info'].append(f'Google Cloud Project: {self.google_cloud_project}')
        
        # Check Google Cloud Credentials (optional)
        if not self.google_application_credentials:
            status['warnings'].append(
                'Google Application Credentials not set. Vertex AI features may not work.'
            )
        elif not os.path.exists(self.google_application_credentials):
            status['warnings'].append(
                f'Google Application Credentials file not found: {self.google_application_credentials}'
            )
        else:
            status['info'].append('Google Application Credentials found')
        
        return status
    
    def get_api_key_info(self) -> dict:
        """
        Get information about the API key source (for debugging)
        """
        api_key_env_name = os.getenv('GOOGLE_API_KEY_ENV_NAME', 'Google API')
        
        return {
            'primary_env_var': api_key_env_name,
            'api_key_found': bool(self.google_api_key),
            'api_key_length': len(self.google_api_key) if self.google_api_key else 0,
            'api_key_preview': f"{self.google_api_key[:8]}..." if self.google_api_key else None
        }

# Global configuration instance
config = Config()

def get_config() -> Config:
    """Get the global configuration instance"""
    return config

def get_google_api_key() -> Optional[str]:
    """Get the Google API key"""
    return config.google_api_key

def validate_config() -> dict:
    """Validate the current configuration"""
    return config.validate_configuration()
