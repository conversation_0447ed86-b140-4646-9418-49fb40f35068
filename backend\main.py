from fastapi import <PERSON>AP<PERSON>, HTTPException, UploadFile, File, Form, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel
from typing import Optional, List
import os
import json
import uuid
import asyncio
from datetime import datetime
import logging

from veo_client import <PERSON>eoClient
from genai_client import get_genai_client
from config import get_config, validate_config
from models import (
    TextToVideoRequest,
    ImageToVideoRequest,
    VideoResponse,
    OperationStatusResponse,
    VideoListResponse
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Google Veo API Server",
    description="Backend API for Google Veo video generation",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Veo client
veo_client = VeoClient()

# In-memory storage for operations and videos (in production, use a database)
operations_store = {}
videos_store = {}

@app.on_startup
async def startup_event():
    """Initialize the application"""
    logger.info("Starting Google Veo API Server...")

    # Verify Google Cloud credentials
    try:
        await veo_client.verify_credentials()
        logger.info("Google Cloud credentials verified successfully")
    except Exception as e:
        logger.error(f"Failed to verify Google Cloud credentials: {e}")
        logger.error("Please ensure GOOGLE_APPLICATION_CREDENTIALS is set correctly")

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Google Veo API Server is running",
        "version": "1.0.0",
        "status": "healthy"
    }

@app.get("/api/health")
async def health_check():
    """Detailed health check"""
    try:
        # Check configuration
        config_status = validate_config()

        # Check Google Cloud connection
        credentials_valid = await veo_client.verify_credentials()

        # Check GenAI client
        genai_client = get_genai_client()
        genai_status = genai_client.test_connection()

        return {
            "status": "healthy" if config_status['valid'] else "warning",
            "timestamp": datetime.utcnow().isoformat(),
            "services": {
                "google_cloud": "connected" if credentials_valid else "disconnected",
                "veo_api": "available",
                "genai_api": "connected" if genai_status['test_successful'] else "disconnected"
            },
            "configuration": {
                "api_key_available": config_status['valid'],
                "errors": config_status['errors'],
                "warnings": config_status['warnings']
            },
            "genai_test": genai_status
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
        )

@app.post("/api/generate/text-to-video")
async def generate_text_to_video(request: TextToVideoRequest):
    """Generate video from text prompt"""
    try:
        logger.info(f"Starting text-to-video generation: {request.prompt[:100]}...")

        # Generate unique operation ID
        operation_id = str(uuid.uuid4())

        # Store operation in memory
        operations_store[operation_id] = {
            "id": operation_id,
            "type": "text-to-video",
            "status": "running",
            "progress": 0,
            "message": "Initializing video generation...",
            "created_at": datetime.utcnow().isoformat(),
            "request_data": request.dict()
        }

        # Start video generation in background
        asyncio.create_task(
            veo_client.generate_text_to_video_async(operation_id, request, operations_store, videos_store)
        )

        return {"operation_id": operation_id}

    except Exception as e:
        logger.error(f"Error starting text-to-video generation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate/image-to-video")
async def generate_image_to_video(
    image: UploadFile = File(...),
    prompt: str = Form(""),
    negativePrompt: str = Form(""),
    durationSeconds: int = Form(8),
    aspectRatio: str = Form("16:9"),
    sampleCount: int = Form(1),
    enhancePrompt: bool = Form(True),
    personGeneration: str = Form("allow_adult")
):
    """Generate video from image and optional text prompt"""
    try:
        logger.info(f"Starting image-to-video generation with image: {image.filename}")

        # Validate image file
        if not image.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")

        # Read image data
        image_data = await image.read()

        # Create request object
        request = ImageToVideoRequest(
            prompt=prompt,
            negativePrompt=negativePrompt,
            durationSeconds=durationSeconds,
            aspectRatio=aspectRatio,
            sampleCount=sampleCount,
            enhancePrompt=enhancePrompt,
            personGeneration=personGeneration
        )

        # Generate unique operation ID
        operation_id = str(uuid.uuid4())

        # Store operation in memory
        operations_store[operation_id] = {
            "id": operation_id,
            "type": "image-to-video",
            "status": "running",
            "progress": 0,
            "message": "Processing image and initializing video generation...",
            "created_at": datetime.utcnow().isoformat(),
            "request_data": request.dict()
        }

        # Start video generation in background
        asyncio.create_task(
            veo_client.generate_image_to_video_async(
                operation_id, request, image_data, image.content_type,
                operations_store, videos_store
            )
        )

        return {"operation_id": operation_id}

    except Exception as e:
        logger.error(f"Error starting image-to-video generation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/operations/{operation_id}/status")
async def get_operation_status(operation_id: str):
    """Get the status of a video generation operation"""
    if operation_id not in operations_store:
        raise HTTPException(status_code=404, detail="Operation not found")

    operation = operations_store[operation_id]

    response = OperationStatusResponse(
        operation_id=operation_id,
        status=operation["status"],
        progress=operation["progress"],
        message=operation["message"],
        created_at=operation["created_at"]
    )

    # Include videos if completed
    if operation["status"] == "completed" and "video_ids" in operation:
        response.videos = [videos_store[vid_id] for vid_id in operation["video_ids"] if vid_id in videos_store]

    # Include error if failed
    if operation["status"] == "failed" and "error" in operation:
        response.error = operation["error"]

    return response

@app.get("/api/videos")
async def get_videos():
    """Get list of all generated videos"""
    videos = list(videos_store.values())
    videos.sort(key=lambda x: x["created_at"], reverse=True)

    return VideoListResponse(videos=videos)

@app.get("/api/videos/{video_id}")
async def get_video(video_id: str):
    """Get specific video details"""
    if video_id not in videos_store:
        raise HTTPException(status_code=404, detail="Video not found")

    return videos_store[video_id]

@app.delete("/api/videos/{video_id}")
async def delete_video(video_id: str):
    """Delete a video"""
    if video_id not in videos_store:
        raise HTTPException(status_code=404, detail="Video not found")

    # Remove from storage
    del videos_store[video_id]

    return {"message": "Video deleted successfully"}

@app.get("/api/videos/{video_id}/download")
async def download_video(video_id: str):
    """Download a video file"""
    if video_id not in videos_store:
        raise HTTPException(status_code=404, detail="Video not found")

    video = videos_store[video_id]

    # For now, redirect to the GCS URL
    # In production, you might want to proxy the download
    return {"download_url": video["uri"]}

@app.post("/api/test/genai")
async def test_genai(prompt: str = "Hello, how are you?"):
    """Test Google GenAI SDK functionality"""
    try:
        genai_client = get_genai_client()

        if not genai_client.is_available():
            raise HTTPException(
                status_code=503,
                detail="Google GenAI client not available. Check API key configuration."
            )

        # Test content generation using exact documentation syntax
        response = await genai_client.generate_content(contents=prompt)

        if response:
            return {
                "success": True,
                "contents": prompt,
                "response": response,
                "model": "gemini-2.0-flash"
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to generate content"
            )

    except Exception as e:
        logger.error(f"GenAI test failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
