@echo off
setlocal enabledelayedexpansion
echo ========================================
echo Google Veo Backend - Test Script
echo ========================================
echo.

REM Check if Python is installed
echo [1/4] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Python is not installed or not in PATH
    echo    Please install Python from https://python.org/
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ✅ Python found: !PYTHON_VERSION!

REM Check if .env file exists
echo.
echo [2/4] Checking environment configuration...
if not exist .env (
    echo ⚠️  Warning: .env file not found
    echo    Please copy .env.example to .env and configure your Google Cloud settings
    echo.
    if exist .env.example (
        echo    Copying .env.example to .env...
        copy .env.example .env >nul
        if %errorlevel% equ 0 (
            echo ✅ .env file created from template
            echo ⚠️  Please edit .env file with your Google Cloud configuration before continuing
        ) else (
            echo ❌ Failed to copy .env.example to .env
        )
        echo.
    ) else (
        echo ❌ .env.example file not found
        echo    Please create .env file manually with your Google Cloud configuration
        echo.
    )
) else (
    echo ✅ Environment configuration file found
)

REM Virtual Environment Management
echo.
echo [3/4] Managing Python virtual environment...

REM Check if virtual environment exists
if not exist backend\venv (
    echo 🐍 Creating Python virtual environment...
    cd backend
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ Error: Failed to create virtual environment
        echo    Please ensure Python is properly installed and has venv module
        echo    You may need to install python-venv package on some systems
        cd ..
        echo.
        pause
        exit /b 1
    )
    cd ..
    echo ✅ Virtual environment created successfully
) else (
    echo ✅ Virtual environment already exists
)

REM Test virtual environment activation
echo.
echo [4/4] Testing virtual environment and dependencies...
cd backend
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ Error: Failed to activate virtual environment
    echo    The virtual environment may be corrupted. Please delete backend\venv and try again
    cd ..
    echo.
    pause
    exit /b 1
)
echo ✅ Virtual environment activated successfully

REM Check if dependencies need to be installed/updated
set NEED_INSTALL=0

REM Check if pip-installed packages list exists (we'll create a marker file)
if not exist venv\.dependencies_installed (
    set NEED_INSTALL=1
    echo 📦 First-time dependency installation required
) else (
    echo 🔍 Checking if dependencies are working...
    REM For simplicity, we'll check if key packages are importable
    python -c "import fastapi, uvicorn" >nul 2>&1
    if !errorlevel! neq 0 (
        set NEED_INSTALL=1
        echo 📦 Missing or corrupted dependencies detected
    ) else (
        echo ✅ Core dependencies appear to be installed correctly
    )
)

REM Install dependencies if needed
if !NEED_INSTALL! equ 1 (
    echo 📦 Installing/updating Python dependencies...
    echo    This may take a few minutes...

    REM Try to upgrade pip, but don't fail if it has permission issues
    echo    Checking pip version...
    pip install --upgrade pip >nul 2>&1
    if !errorlevel! neq 0 (
        echo ⚠️  Note: Could not upgrade pip (permission issue), continuing with current version
        echo    This is usually not a problem for dependency installation
    ) else (
        echo ✅ Pip upgraded successfully
    )

    pip install -r requirements.txt
    if !errorlevel! neq 0 (
        echo ❌ Error: Failed to install backend dependencies
        echo    Please check your internet connection and requirements.txt file
        call venv\Scripts\deactivate.bat
        cd ..
        echo.
        pause
        exit /b 1
    )

    REM Create marker file to indicate successful installation
    echo Dependencies installed on %date% %time% > venv\.dependencies_installed
    echo ✅ Backend dependencies installed/updated successfully
) else (
    echo ✅ Backend dependencies are up to date
)

echo.
echo ========================================
echo ✅ Backend Environment Test Complete
echo ========================================
echo.
echo 🎯 Test Results:
echo    • Python: Working
echo    • Virtual Environment: Created and activated
echo    • Dependencies: Installed and verified
echo    • Environment: Ready for backend server
echo.
echo 💡 Next Steps:
echo    1. Configure your .env file with Google Cloud credentials
echo    2. Run 'python main.py' to start the backend server
echo    3. Test with 'python ..\test-backend.py' from project root
echo.
echo 🔧 To start the backend server now:
echo    python main.py
echo.

REM Deactivate virtual environment
call venv\Scripts\deactivate.bat
cd ..

echo Press any key to exit...
pause >nul
