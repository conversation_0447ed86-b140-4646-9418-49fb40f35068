# Google Veo AI Video Generator - PowerShell Startup Script
# This script ensures Node.js is in PATH and runs the full startup process

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Google Veo AI Video Generator - Startup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Add Node.js to PATH for this session
$env:PATH += ";C:\Program Files\nodejs"

# Step 1: Check Node.js
Write-Host "[1/8] Checking Node.js installation..." -ForegroundColor Yellow
try {
    $nodeVersion = & node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js not found"
    }
} catch {
    Write-Host "❌ Error: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "   Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check npm
try {
    $npmVersion = & npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ npm found: $npmVersion" -ForegroundColor Green
    } else {
        throw "npm not found"
    }
} catch {
    Write-Host "❌ Error: npm is not installed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 2: Check Python
Write-Host ""
Write-Host "[2/8] Checking Python installation..." -ForegroundColor Yellow
try {
    $pythonVersion = & python --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "❌ Error: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "   Please install Python from https://python.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 3: Check .env file
Write-Host ""
Write-Host "[3/8] Checking environment configuration..." -ForegroundColor Yellow
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  Warning: .env file not found" -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Write-Host "   Copying .env.example to .env..." -ForegroundColor Yellow
        Copy-Item ".env.example" ".env"
        Write-Host "✅ Created .env file from template" -ForegroundColor Green
        Write-Host "⚠️  Please edit .env file with your Google Cloud configuration" -ForegroundColor Yellow
    } else {
        Write-Host "❌ .env.example file not found" -ForegroundColor Red
    }
} else {
    Write-Host "✅ Environment file (.env) found" -ForegroundColor Green
}

# Step 4: Virtual Environment
Write-Host ""
Write-Host "[4/8] Setting up Python virtual environment..." -ForegroundColor Yellow
if (-not (Test-Path "backend\venv")) {
    Write-Host "⚠️  Virtual environment not found, creating..." -ForegroundColor Yellow
    Set-Location "backend"
    & python -m venv venv
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Error: Failed to create virtual environment" -ForegroundColor Red
        Set-Location ".."
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host "✅ Virtual environment created successfully" -ForegroundColor Green
    Set-Location ".."
} else {
    Write-Host "✅ Virtual environment found at backend\venv" -ForegroundColor Green
}

# Step 5: Python Dependencies
Write-Host ""
Write-Host "[5/8] Installing Python dependencies..." -ForegroundColor Yellow
Set-Location "backend"

# Activate virtual environment and install dependencies
Write-Host "   Activating virtual environment..." -ForegroundColor Cyan
& .\venv\Scripts\Activate.ps1
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Error: Failed to activate virtual environment" -ForegroundColor Red
    Set-Location ".."
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "   Installing/updating Python dependencies..." -ForegroundColor Cyan
& pip install -r ..\requirements.txt
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Error: Failed to install Python dependencies" -ForegroundColor Red
    Set-Location ".."
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host "✅ Python dependencies installed/updated successfully" -ForegroundColor Green

Set-Location ".."

# Step 6: Frontend Dependencies
Write-Host ""
Write-Host "[6/8] Installing frontend dependencies..." -ForegroundColor Yellow
Write-Host "   Installing Node.js packages..." -ForegroundColor Cyan
& npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Error: Failed to install frontend dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host "✅ Frontend dependencies installed successfully" -ForegroundColor Green

# Step 7: Start Backend Server
Write-Host ""
Write-Host "[7/8] Starting backend server..." -ForegroundColor Yellow
Write-Host "🚀 Starting backend server in background..." -ForegroundColor Cyan

# Start backend in a new window
$backendJob = Start-Process powershell -ArgumentList "-Command", "cd 'backend'; .\venv\Scripts\Activate.ps1; python main.py" -WindowStyle Normal -PassThru

# Wait a moment for backend to start
Start-Sleep -Seconds 3

# Step 8: Start Frontend Server
Write-Host ""
Write-Host "[8/8] Starting frontend development server..." -ForegroundColor Yellow
Write-Host "🚀 Starting frontend development server..." -ForegroundColor Cyan
Write-Host ""
Write-Host "⏳ Please wait while the development server starts..." -ForegroundColor Cyan
Write-Host "   This may take a few moments on first run..." -ForegroundColor Cyan
Write-Host ""

# Start frontend server (this will block)
& npm run dev

# Cleanup message
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🎉 Google Veo AI Video Generator" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🌐 Application URLs:" -ForegroundColor Green
Write-Host "   • Backend API: http://localhost:8000" -ForegroundColor White
Write-Host "   • Frontend UI:  http://localhost:3000" -ForegroundColor White
Write-Host "   • API Docs:    http://localhost:8000/docs" -ForegroundColor White
Write-Host "   • Health Check: http://localhost:8000/api/health" -ForegroundColor White
Write-Host ""
Write-Host "💡 Tips:" -ForegroundColor Yellow
Write-Host "   • Press Ctrl+C to stop the frontend server" -ForegroundColor White
Write-Host "   • Backend server runs in a separate window" -ForegroundColor White
Write-Host "   • Check the health endpoint to verify API key configuration" -ForegroundColor White
Write-Host ""
Read-Host "Press Enter to close this window"
