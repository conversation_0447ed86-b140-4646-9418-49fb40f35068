import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, X, Play, Loader2, Wand2, Image as ImageIcon } from 'lucide-react'
import toast from 'react-hot-toast'
import { videoAPI } from '../utils/api'
import VideoPlayer from './VideoPlayer'
import OperationStatus from './OperationStatus'

const ImageToVideo = () => {
  const [formData, setFormData] = useState({
    prompt: '',
    negativePrompt: '',
    durationSeconds: 8,
    aspectRatio: '16:9',
    sampleCount: 1,
    enhancePrompt: true,
    personGeneration: 'allow_adult',
  })
  const [selectedImage, setSelectedImage] = useState(null)
  const [imagePreview, setImagePreview] = useState(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [operationId, setOperationId] = useState(null)
  const [generatedVideos, setGeneratedVideos] = useState([])

  const onDrop = useCallback((acceptedFiles) => {
    const file = acceptedFiles[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select a valid image file')
        return
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('Image file size must be less than 10MB')
        return
      }

      setSelectedImage(file)
      
      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target.result)
      }
      reader.readAsDataURL(file)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    },
    multiple: false
  })

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const removeImage = () => {
    setSelectedImage(null)
    setImagePreview(null)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!selectedImage) {
      toast.error('Please select an image for video generation')
      return
    }

    setIsGenerating(true)
    setOperationId(null)
    setGeneratedVideos([])

    try {
      const formDataToSend = new FormData()
      formDataToSend.append('image', selectedImage)
      formDataToSend.append('prompt', formData.prompt)
      formDataToSend.append('negativePrompt', formData.negativePrompt)
      formDataToSend.append('durationSeconds', formData.durationSeconds.toString())
      formDataToSend.append('aspectRatio', formData.aspectRatio)
      formDataToSend.append('sampleCount', formData.sampleCount.toString())
      formDataToSend.append('enhancePrompt', formData.enhancePrompt.toString())
      formDataToSend.append('personGeneration', formData.personGeneration)

      const response = await videoAPI.generateImageToVideo(formDataToSend)
      setOperationId(response.operation_id)
      toast.success('Video generation started! This may take a few minutes.')
    } catch (error) {
      console.error('Error generating video:', error)
      toast.error(error.response?.data?.detail || 'Failed to start video generation')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleOperationComplete = (videos) => {
    setGeneratedVideos(videos)
    setOperationId(null)
    toast.success(`Successfully generated ${videos.length} video(s)!`)
  }

  const handleOperationError = (error) => {
    setOperationId(null)
    toast.error(error)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Image to Video</h2>
        <p className="text-gray-600">Bring your images to life with AI-generated video animations</p>
      </div>

      {/* Generation Form */}
      <div className="card max-w-4xl mx-auto">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Upload Image *
            </label>
            
            {!selectedImage ? (
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                  isDragActive
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <input {...getInputProps()} />
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900 mb-2">
                  {isDragActive ? 'Drop your image here' : 'Upload an image'}
                </p>
                <p className="text-sm text-gray-500">
                  Drag and drop or click to select • JPEG, PNG, WebP • Max 10MB
                </p>
                <p className="text-xs text-gray-400 mt-2">
                  Recommended: 1280x720 or 720x1280 pixels for best results
                </p>
              </div>
            ) : (
              <div className="relative">
                <img
                  src={imagePreview}
                  alt="Selected"
                  className="w-full max-w-md mx-auto rounded-lg shadow-md"
                />
                <button
                  type="button"
                  onClick={removeImage}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
                <div className="mt-2 text-center">
                  <p className="text-sm text-gray-600">{selectedImage.name}</p>
                  <p className="text-xs text-gray-400">
                    {(selectedImage.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Optional Prompt */}
          <div>
            <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-2">
              Additional Description (Optional)
            </label>
            <textarea
              id="prompt"
              name="prompt"
              value={formData.prompt}
              onChange={handleInputChange}
              placeholder="Add additional context or describe the motion you want... (e.g., 'gentle swaying motion', 'camera slowly zooming in')"
              className="input-field h-24 resize-none"
            />
            <p className="text-xs text-gray-500 mt-1">
              Leave empty to let AI decide the best animation for your image
            </p>
          </div>

          {/* Negative Prompt */}
          <div>
            <label htmlFor="negativePrompt" className="block text-sm font-medium text-gray-700 mb-2">
              Negative Prompt (Optional)
            </label>
            <textarea
              id="negativePrompt"
              name="negativePrompt"
              value={formData.negativePrompt}
              onChange={handleInputChange}
              placeholder="Describe what you don't want in the video..."
              className="input-field h-20 resize-none"
            />
          </div>

          {/* Settings Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Duration */}
            <div>
              <label htmlFor="durationSeconds" className="block text-sm font-medium text-gray-700 mb-2">
                Duration (seconds)
              </label>
              <select
                id="durationSeconds"
                name="durationSeconds"
                value={formData.durationSeconds}
                onChange={handleInputChange}
                className="input-field"
              >
                <option value={5}>5 seconds</option>
                <option value={6}>6 seconds</option>
                <option value={7}>7 seconds</option>
                <option value={8}>8 seconds</option>
              </select>
            </div>

            {/* Aspect Ratio */}
            <div>
              <label htmlFor="aspectRatio" className="block text-sm font-medium text-gray-700 mb-2">
                Aspect Ratio
              </label>
              <select
                id="aspectRatio"
                name="aspectRatio"
                value={formData.aspectRatio}
                onChange={handleInputChange}
                className="input-field"
              >
                <option value="16:9">16:9 (Landscape)</option>
                <option value="9:16">9:16 (Portrait)</option>
              </select>
            </div>

            {/* Sample Count */}
            <div>
              <label htmlFor="sampleCount" className="block text-sm font-medium text-gray-700 mb-2">
                Number of Videos
              </label>
              <select
                id="sampleCount"
                name="sampleCount"
                value={formData.sampleCount}
                onChange={handleInputChange}
                className="input-field"
              >
                <option value={1}>1 video</option>
                <option value={2}>2 videos</option>
                <option value={3}>3 videos</option>
                <option value={4}>4 videos</option>
              </select>
            </div>
          </div>

          {/* Advanced Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Advanced Settings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Person Generation */}
              <div>
                <label htmlFor="personGeneration" className="block text-sm font-medium text-gray-700 mb-2">
                  Person Generation
                </label>
                <select
                  id="personGeneration"
                  name="personGeneration"
                  value={formData.personGeneration}
                  onChange={handleInputChange}
                  className="input-field"
                >
                  <option value="allow_adult">Allow Adults</option>
                  <option value="dont_allow">Don't Allow People</option>
                </select>
              </div>

              {/* Enhance Prompt */}
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="enhancePrompt"
                  name="enhancePrompt"
                  checked={formData.enhancePrompt}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="enhancePrompt" className="text-sm font-medium text-gray-700">
                  Enhance prompt with Gemini
                </label>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-center">
            <button
              type="submit"
              disabled={isGenerating || !selectedImage}
              className="btn-primary flex items-center space-x-2 px-8 py-3 text-lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <Wand2 className="h-5 w-5" />
                  <span>Generate Video</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Operation Status */}
      {operationId && (
        <OperationStatus
          operationId={operationId}
          onComplete={handleOperationComplete}
          onError={handleOperationError}
        />
      )}

      {/* Generated Videos */}
      {generatedVideos.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-900 text-center">Generated Videos</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {generatedVideos.map((video, index) => (
              <VideoPlayer
                key={index}
                video={video}
                title={`Generated Video ${index + 1}`}
                prompt={formData.prompt || 'Image to Video'}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default ImageToVideo
