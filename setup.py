#!/usr/bin/env python3
"""
Setup script for Google Veo AI Video Generator
Creates virtual environment and installs all dependencies
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, cwd=None, shell=False):
    """Run a command and return success status"""
    try:
        print(f"Running: {' '.join(command) if isinstance(command, list) else command}")
        result = subprocess.run(
            command, 
            cwd=cwd, 
            shell=shell, 
            check=True,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False

def setup_frontend():
    """Setup frontend dependencies"""
    print("\n🔧 Setting up frontend...")
    
    # Check if node_modules exists
    if Path("node_modules").exists():
        print("✅ Frontend dependencies already installed")
        return True
    
    # Install npm dependencies
    if not run_command(["npm", "install"]):
        print("❌ Failed to install frontend dependencies")
        return False
    
    print("✅ Frontend dependencies installed successfully")
    return True

def setup_backend():
    """Setup backend with virtual environment"""
    print("\n🔧 Setting up backend...")
    
    backend_dir = Path("backend")
    venv_dir = backend_dir / "venv"
    
    # Create virtual environment if it doesn't exist
    if not venv_dir.exists():
        print("Creating Python virtual environment...")
        python_cmd = "python3" if platform.system() != "Windows" else "python"
        
        if not run_command([python_cmd, "-m", "venv", "venv"], cwd=backend_dir):
            print("❌ Failed to create virtual environment")
            return False
        print("✅ Virtual environment created")
    else:
        print("✅ Virtual environment already exists")
    
    # Determine activation script path
    if platform.system() == "Windows":
        activate_script = venv_dir / "Scripts" / "activate.bat"
        pip_path = venv_dir / "Scripts" / "pip.exe"
    else:
        activate_script = venv_dir / "bin" / "activate"
        pip_path = venv_dir / "bin" / "pip"
    
    # Install requirements in virtual environment
    print("Installing Python dependencies in virtual environment...")
    
    if platform.system() == "Windows":
        # On Windows, we need to use the pip from the venv directly
        if not run_command([str(pip_path), "install", "-r", "requirements.txt"], cwd=backend_dir):
            print("❌ Failed to install Python dependencies")
            return False
    else:
        # On Unix systems, we can use the pip from the venv
        if not run_command([str(pip_path), "install", "-r", "requirements.txt"], cwd=backend_dir):
            print("❌ Failed to install Python dependencies")
            return False
    
    print("✅ Backend dependencies installed successfully")
    return True

def setup_environment():
    """Setup environment configuration"""
    print("\n🔧 Setting up environment configuration...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if env_example.exists():
        print("Copying .env.example to .env...")
        try:
            with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                dst.write(src.read())
            print("✅ .env file created from template")
            print("⚠️  Please edit .env file with your Google Cloud configuration")
            return True
        except Exception as e:
            print(f"❌ Failed to copy .env.example: {e}")
            return False
    else:
        print("❌ .env.example not found")
        return False

def create_run_scripts():
    """Create convenient run scripts"""
    print("\n🔧 Creating run scripts...")
    
    # Create run-backend script
    if platform.system() == "Windows":
        backend_script = Path("run-backend.bat")
        with open(backend_script, 'w') as f:
            f.write("""@echo off
cd backend
call venv\\Scripts\\activate.bat
python main.py
pause
""")
        print("✅ Created run-backend.bat")
    else:
        backend_script = Path("run-backend.sh")
        with open(backend_script, 'w') as f:
            f.write("""#!/bin/bash
cd backend
source venv/bin/activate
python main.py
""")
        os.chmod(backend_script, 0o755)
        print("✅ Created run-backend.sh")
    
    # Create run-frontend script
    if platform.system() == "Windows":
        frontend_script = Path("run-frontend.bat")
        with open(frontend_script, 'w') as f:
            f.write("""@echo off
npm run dev
pause
""")
        print("✅ Created run-frontend.bat")
    else:
        frontend_script = Path("run-frontend.sh")
        with open(frontend_script, 'w') as f:
            f.write("""#!/bin/bash
npm run dev
""")
        os.chmod(frontend_script, 0o755)
        print("✅ Created run-frontend.sh")

def main():
    """Main setup function"""
    print("🚀 Google Veo AI Video Generator - Setup")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("package.json").exists() or not Path("backend").exists():
        print("❌ Please run this script from the project root directory")
        sys.exit(1)
    
    success = True
    
    # Setup frontend
    success &= setup_frontend()
    
    # Setup backend
    success &= setup_backend()
    
    # Setup environment
    success &= setup_environment()
    
    # Create run scripts
    create_run_scripts()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Setup completed successfully!")
        print("\n📝 Next steps:")
        print("1. Edit .env file with your Google Cloud configuration")
        print("2. Run setup verification: python setup-check.py")
        print("3. Start the application:")
        if platform.system() == "Windows":
            print("   - start.bat (both servers)")
            print("   - run-backend.bat (backend only)")
            print("   - run-frontend.bat (frontend only)")
        else:
            print("   - ./start.sh (both servers)")
            print("   - ./run-backend.sh (backend only)")
            print("   - ./run-frontend.sh (frontend only)")
        print("4. Open http://localhost:3000 in your browser")
    else:
        print("⚠️  Setup completed with some issues")
        print("Please check the error messages above and try again")
    
    print("\n📖 For detailed instructions, see README.md or SETUP_GUIDE.md")

if __name__ == "__main__":
    main()
