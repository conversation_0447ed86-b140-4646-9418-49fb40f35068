@echo off
setlocal enabledelayedexpansion
echo ========================================
echo Testing Virtual Environment Management
echo ========================================
echo.

REM Check if Python is installed
echo [1/4] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Python is not installed or not in PATH
    echo    Please install Python from https://python.org/
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ✅ Python found: !PYTHON_VERSION!

REM Virtual Environment Management
echo.
echo [2/4] Checking Python virtual environment...

REM Check if virtual environment exists
if not exist backend\venv (
    echo ⚠️  Virtual environment not found, creating...
    cd backend
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ Error: Failed to create virtual environment
        echo    Please ensure Python is properly installed and has venv module
        cd ..
        echo.
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created successfully
    cd ..
) else (
    echo ✅ Virtual environment found at backend\venv
)

REM Test virtual environment activation
echo.
echo [3/4] Testing virtual environment activation...
cd backend
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ Error: Failed to activate virtual environment
    echo    Virtual environment may be corrupted, please delete backend\venv and try again
    cd ..
    echo.
    pause
    exit /b 1
)
echo ✅ Virtual environment activated successfully

REM Check if requirements.txt exists
if not exist requirements.txt (
    echo ❌ Error: requirements.txt not found in backend directory
    echo    Please ensure requirements.txt exists
    call venv\Scripts\deactivate.bat >nul 2>&1
    cd ..
    echo.
    pause
    exit /b 1
)

REM Check Python and pip in virtual environment
echo.
echo [4/4] Testing virtual environment functionality...
echo    Checking Python in virtual environment...
python --version
if %errorlevel% neq 0 (
    echo ❌ Error: Python not working in virtual environment
    call venv\Scripts\deactivate.bat >nul 2>&1
    cd ..
    echo.
    pause
    exit /b 1
)

echo    Checking pip in virtual environment...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Pip not working in virtual environment
    call venv\Scripts\deactivate.bat >nul 2>&1
    cd ..
    echo.
    pause
    exit /b 1
)

echo    Testing pip functionality...
pip list >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Pip list command failed
    call venv\Scripts\deactivate.bat >nul 2>&1
    cd ..
    echo.
    pause
    exit /b 1
)

echo ✅ Virtual environment is fully functional

REM Test dependency installation (just a few key packages)
echo.
echo Testing dependency installation...
echo    Installing a test package (requests)...
pip install requests >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Failed to install test package
    call venv\Scripts\deactivate.bat >nul 2>&1
    cd ..
    echo.
    pause
    exit /b 1
)

echo    Verifying package installation...
pip show requests >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Test package not properly installed
    call venv\Scripts\deactivate.bat >nul 2>&1
    cd ..
    echo.
    pause
    exit /b 1
)

echo ✅ Package installation working correctly

REM Cleanup
call venv\Scripts\deactivate.bat >nul 2>&1
cd ..

echo.
echo ========================================
echo ✅ Virtual Environment Test Completed
echo ========================================
echo.
echo 🎉 All virtual environment functionality is working:
echo    • Virtual environment creation: ✅
echo    • Environment activation: ✅
echo    • Python functionality: ✅
echo    • Pip functionality: ✅
echo    • Package installation: ✅
echo.
echo 💡 The start.bat script's virtual environment management
echo    should work perfectly on systems with Node.js installed.
echo.
echo Press any key to exit...
pause >nul
