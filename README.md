# Google Veo AI Video Generator

A complete web application for generating videos using Google's Veo AI model. This application provides both text-to-video and image-to-video generation capabilities with a modern, intuitive user interface.

## Features

- **Text-to-Video Generation**: Create videos from descriptive text prompts
- **Image-to-Video Generation**: Animate static images with AI-generated motion
- **Real-time Progress Tracking**: Monitor video generation status with live updates
- **Video Gallery**: Browse, preview, and manage generated videos
- **Advanced Controls**: Configure duration, aspect ratio, and generation parameters
- **Download Support**: Download generated videos in MP4 format
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Technology Stack

### Frontend
- **React 18** with Vite for fast development
- **Tailwind CSS** for modern styling
- **Lucide React** for beautiful icons
- **React Hot Toast** for notifications
- **React Dropzone** for file uploads

### Backend
- **FastAPI** for high-performance API server
- **Google Cloud AI Platform** for Veo API integration
- **Google Cloud Storage** for video storage
- **Pydantic** for data validation
- **Async/await** for non-blocking operations

## Prerequisites

Before setting up the application, ensure you have:

1. **Node.js 18+** and **npm** installed
2. **Python 3.8+** installed
3. **Google Cloud Project** with billing enabled
4. **Google Cloud SDK** installed and configured
5. **Veo API access** (currently in limited preview)

## Google Cloud Setup

### 1. Create a Google Cloud Project

```bash
# Create a new project
gcloud projects create your-project-id

# Set the project as default
gcloud config set project your-project-id
```

### 2. Enable Required APIs

```bash
# Enable Vertex AI API
gcloud services enable aiplatform.googleapis.com

# Enable Cloud Storage API (optional, for video storage)
gcloud services enable storage.googleapis.com
```

### 3. Create Service Account

```bash
# Create service account
gcloud iam service-accounts create veo-service-account \
    --description="Service account for Veo video generation" \
    --display-name="Veo Service Account"

# Grant necessary permissions
gcloud projects add-iam-policy-binding your-project-id \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/aiplatform.user"

# Create and download service account key
gcloud iam service-accounts keys create veo-service-account-key.json \
    --iam-account=<EMAIL>
```

### 4. Create Storage Bucket (Optional)

```bash
# Create bucket for storing generated videos
gsutil mb gs://your-storage-bucket-name

# Grant storage permissions to service account
gsutil iam ch serviceAccount:<EMAIL>:objectAdmin gs://your-storage-bucket-name
```

## Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd google-veo-ui
```

### 2. Install Frontend Dependencies

```bash
npm install
```

### 3. Install Backend Dependencies

```bash
# Option A: Use the setup script (recommended)
python setup.py

# Option B: Manual setup with virtual environment
cd backend
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate.bat
# Linux/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
cd ..
```

### 4. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

Update the `.env` file with your Google Cloud configuration:

```env
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=./veo-service-account-key.json
GOOGLE_CLOUD_STORAGE_BUCKET=your-storage-bucket-name
VEO_MODEL_ID=veo-2.0-generate-001
```

## Running the Application

### 1. Start the Backend Server

```bash
cd backend
python main.py
```

The backend server will start on `http://localhost:8000`

### 2. Start the Frontend Development Server

```bash
# In a new terminal, from the project root
npm run dev
```

The frontend will start on `http://localhost:3000`

### 3. Access the Application

Open your browser and navigate to `http://localhost:3000`

## Usage

### Text-to-Video Generation

1. Navigate to the "Text to Video" tab
2. Enter a descriptive prompt (e.g., "A fast-tracking shot through a bustling dystopian sprawl with bright neon signs")
3. Configure generation parameters:
   - Duration (5-8 seconds)
   - Aspect ratio (16:9 or 9:16)
   - Number of videos (1-4)
   - Advanced settings (person generation, prompt enhancement)
4. Click "Generate Video"
5. Monitor progress in real-time
6. Preview and download generated videos

### Image-to-Video Generation

1. Navigate to the "Image to Video" tab
2. Upload an image (JPEG, PNG, WebP, max 10MB)
3. Optionally add a text prompt for additional guidance
4. Configure generation parameters
5. Click "Generate Video"
6. Monitor progress and download results

### Video Gallery

- Browse all generated videos
- Search and filter by type
- Sort by date or title
- Delete unwanted videos
- Download videos for offline use

## API Endpoints

### Video Generation
- `POST /api/generate/text-to-video` - Generate video from text
- `POST /api/generate/image-to-video` - Generate video from image

### Operation Management
- `GET /api/operations/{operation_id}/status` - Check generation status

### Video Management
- `GET /api/videos` - List all videos
- `GET /api/videos/{video_id}` - Get video details
- `DELETE /api/videos/{video_id}` - Delete video
- `GET /api/videos/{video_id}/download` - Download video

### Health Check
- `GET /api/health` - Service health status

## Configuration Options

### Veo Model Parameters

- **Duration**: 5-8 seconds (veo-2.0-generate-001)
- **Aspect Ratio**: 16:9 (landscape) or 9:16 (portrait)
- **Sample Count**: 1-4 videos per generation
- **Person Generation**: Allow adults or disable people
- **Prompt Enhancement**: Use Gemini to improve prompts
- **Negative Prompts**: Specify what to avoid in videos

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GOOGLE_CLOUD_PROJECT` | Your Google Cloud project ID | Required |
| `GOOGLE_CLOUD_LOCATION` | Vertex AI location | `us-central1` |
| `GOOGLE_APPLICATION_CREDENTIALS` | Path to service account key | Required |
| `GOOGLE_CLOUD_STORAGE_BUCKET` | Storage bucket for videos | Optional |
| `VEO_MODEL_ID` | Veo model version | `veo-2.0-generate-001` |

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify service account key path
   - Check IAM permissions
   - Ensure APIs are enabled

2. **Video Generation Fails**
   - Check Veo API access
   - Verify project billing
   - Review prompt content policies

3. **Storage Issues**
   - Verify bucket permissions
   - Check bucket exists
   - Ensure storage API is enabled

### Logs and Debugging

- Backend logs: Check console output from Python server
- Frontend logs: Open browser developer tools
- Google Cloud logs: Use Cloud Logging in Google Cloud Console

## Development

### Project Structure

```
google-veo-ui/
├── src/                    # Frontend React application
│   ├── components/         # React components
│   ├── utils/             # Utility functions
│   └── main.jsx           # Application entry point
├── backend/               # Python FastAPI backend
│   ├── main.py           # FastAPI server
│   ├── veo_client.py     # Google Veo API client
│   └── models.py         # Pydantic models
├── public/               # Static assets
└── package.json          # Frontend dependencies
```

### Adding New Features

1. **Frontend**: Add components in `src/components/`
2. **Backend**: Add endpoints in `backend/main.py`
3. **API Client**: Extend `backend/veo_client.py`
4. **Models**: Define data structures in `backend/models.py`

## Production Deployment

### Security Considerations

- Use environment variables for all secrets
- Implement proper authentication
- Set up CORS policies
- Use HTTPS in production
- Implement rate limiting

### Scaling

- Use Google Cloud Run for serverless deployment
- Implement Redis for operation state management
- Use Cloud SQL for persistent video metadata
- Set up load balancing for high traffic

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Support

For issues and questions:

1. Check the troubleshooting section
2. Review Google Cloud documentation
3. Open an issue in the repository
4. Contact Google Cloud support for API-related issues

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

---

**Note**: Google Veo is currently in limited preview. You may need to request access through Google Cloud or Google AI Studio.
