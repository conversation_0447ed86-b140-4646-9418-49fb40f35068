import os
import json
import base64
import asyncio
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, Optional
import httpx
from google.auth import default
from google.auth.transport.requests import Request
from google.cloud import aiplatform
from google.cloud import storage
import io
from PIL import Image

logger = logging.getLogger(__name__)

class VeoClient:
    def __init__(self):
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
        self.location = os.getenv('GOOGLE_CLOUD_LOCATION', 'us-central1')
        self.model_id = os.getenv('VEO_MODEL_ID', 'veo-2.0-generate-001')
        self.storage_bucket = os.getenv('GOOGLE_CLOUD_STORAGE_BUCKET')
        
        # Initialize credentials
        self.credentials = None
        self.storage_client = None
        
        # API endpoints
        self.base_url = f"https://{self.location}-aiplatform.googleapis.com"
        
        logger.info(f"Initialized VeoClient with project: {self.project_id}, location: {self.location}")

    async def verify_credentials(self) -> bool:
        """Verify Google Cloud credentials"""
        try:
            # Get default credentials
            self.credentials, project = default()
            
            if not self.project_id:
                self.project_id = project
            
            # Refresh credentials if needed
            if not self.credentials.valid:
                self.credentials.refresh(Request())
            
            # Initialize storage client
            self.storage_client = storage.Client(credentials=self.credentials, project=self.project_id)
            
            # Test storage access if bucket is configured
            if self.storage_bucket:
                try:
                    bucket = self.storage_client.bucket(self.storage_bucket)
                    bucket.exists()  # This will raise an exception if we can't access the bucket
                    logger.info(f"Storage bucket '{self.storage_bucket}' is accessible")
                except Exception as e:
                    logger.warning(f"Storage bucket '{self.storage_bucket}' is not accessible: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to verify credentials: {e}")
            return False

    def get_access_token(self) -> str:
        """Get a valid access token"""
        if not self.credentials or not self.credentials.valid:
            self.credentials.refresh(Request())
        return self.credentials.token

    async def generate_text_to_video_async(
        self, 
        operation_id: str, 
        request, 
        operations_store: Dict, 
        videos_store: Dict
    ):
        """Generate video from text prompt asynchronously"""
        try:
            # Update operation status
            operations_store[operation_id]["message"] = "Preparing request..."
            operations_store[operation_id]["progress"] = 10
            
            # Prepare the request payload
            payload = {
                "instances": [{
                    "prompt": request.prompt
                }],
                "parameters": {
                    "durationSeconds": request.durationSeconds,
                    "aspectRatio": request.aspectRatio,
                    "sampleCount": request.sampleCount,
                    "enhancePrompt": request.enhancePrompt,
                    "personGeneration": request.personGeneration
                }
            }
            
            if request.negativePrompt:
                payload["parameters"]["negativePrompt"] = request.negativePrompt
            
            if request.seed is not None:
                payload["parameters"]["seed"] = request.seed
            
            if self.storage_bucket:
                payload["parameters"]["storageUri"] = f"gs://{self.storage_bucket}/veo-videos/"
            
            # Update operation status
            operations_store[operation_id]["message"] = "Sending request to Google Veo API..."
            operations_store[operation_id]["progress"] = 20
            
            # Make the API request
            url = f"{self.base_url}/v1/projects/{self.project_id}/locations/{self.location}/publishers/google/models/{self.model_id}:predictLongRunning"
            
            headers = {
                "Authorization": f"Bearer {self.get_access_token()}",
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, json=payload, headers=headers)
                
                if response.status_code != 200:
                    error_msg = f"API request failed: {response.status_code} - {response.text}"
                    logger.error(error_msg)
                    operations_store[operation_id]["status"] = "failed"
                    operations_store[operation_id]["error"] = error_msg
                    return
                
                result = response.json()
                google_operation_name = result.get("name")
                
                if not google_operation_name:
                    error_msg = "No operation name returned from API"
                    logger.error(error_msg)
                    operations_store[operation_id]["status"] = "failed"
                    operations_store[operation_id]["error"] = error_msg
                    return
            
            # Update operation status
            operations_store[operation_id]["message"] = "Video generation in progress..."
            operations_store[operation_id]["progress"] = 30
            operations_store[operation_id]["google_operation_name"] = google_operation_name
            
            # Poll for completion
            await self.poll_operation_status(operation_id, google_operation_name, operations_store, videos_store, request)
            
        except Exception as e:
            logger.error(f"Error in text-to-video generation: {e}")
            operations_store[operation_id]["status"] = "failed"
            operations_store[operation_id]["error"] = str(e)

    async def generate_image_to_video_async(
        self, 
        operation_id: str, 
        request, 
        image_data: bytes,
        image_content_type: str,
        operations_store: Dict, 
        videos_store: Dict
    ):
        """Generate video from image and optional text prompt asynchronously"""
        try:
            # Update operation status
            operations_store[operation_id]["message"] = "Processing image..."
            operations_store[operation_id]["progress"] = 10
            
            # Process and encode image
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # Prepare the request payload
            payload = {
                "instances": [{
                    "image": {
                        "bytesBase64Encoded": image_base64,
                        "mimeType": image_content_type
                    }
                }],
                "parameters": {
                    "durationSeconds": request.durationSeconds,
                    "aspectRatio": request.aspectRatio,
                    "sampleCount": request.sampleCount,
                    "enhancePrompt": request.enhancePrompt,
                    "personGeneration": request.personGeneration
                }
            }
            
            # Add optional prompt
            if request.prompt:
                payload["instances"][0]["prompt"] = request.prompt
            
            if request.negativePrompt:
                payload["parameters"]["negativePrompt"] = request.negativePrompt
            
            if request.seed is not None:
                payload["parameters"]["seed"] = request.seed
            
            if self.storage_bucket:
                payload["parameters"]["storageUri"] = f"gs://{self.storage_bucket}/veo-videos/"
            
            # Update operation status
            operations_store[operation_id]["message"] = "Sending request to Google Veo API..."
            operations_store[operation_id]["progress"] = 20
            
            # Make the API request
            url = f"{self.base_url}/v1/projects/{self.project_id}/locations/{self.location}/publishers/google/models/{self.model_id}:predictLongRunning"
            
            headers = {
                "Authorization": f"Bearer {self.get_access_token()}",
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, json=payload, headers=headers)
                
                if response.status_code != 200:
                    error_msg = f"API request failed: {response.status_code} - {response.text}"
                    logger.error(error_msg)
                    operations_store[operation_id]["status"] = "failed"
                    operations_store[operation_id]["error"] = error_msg
                    return
                
                result = response.json()
                google_operation_name = result.get("name")
                
                if not google_operation_name:
                    error_msg = "No operation name returned from API"
                    logger.error(error_msg)
                    operations_store[operation_id]["status"] = "failed"
                    operations_store[operation_id]["error"] = error_msg
                    return
            
            # Update operation status
            operations_store[operation_id]["message"] = "Video generation in progress..."
            operations_store[operation_id]["progress"] = 30
            operations_store[operation_id]["google_operation_name"] = google_operation_name
            
            # Poll for completion
            await self.poll_operation_status(operation_id, google_operation_name, operations_store, videos_store, request)
            
        except Exception as e:
            logger.error(f"Error in image-to-video generation: {e}")
            operations_store[operation_id]["status"] = "failed"
            operations_store[operation_id]["error"] = str(e)

    async def poll_operation_status(
        self, 
        operation_id: str, 
        google_operation_name: str, 
        operations_store: Dict, 
        videos_store: Dict,
        request
    ):
        """Poll Google Cloud operation status until completion"""
        try:
            max_polls = 120  # 10 minutes with 5-second intervals
            poll_count = 0
            
            while poll_count < max_polls:
                await asyncio.sleep(5)  # Wait 5 seconds between polls
                poll_count += 1
                
                # Update progress
                progress = min(30 + (poll_count * 60 // max_polls), 90)
                operations_store[operation_id]["progress"] = progress
                operations_store[operation_id]["message"] = f"Generating video... ({progress}%)"
                
                # Check operation status
                url = f"{self.base_url}/v1/projects/{self.project_id}/locations/{self.location}/publishers/google/models/{self.model_id}:fetchPredictOperation"
                
                headers = {
                    "Authorization": f"Bearer {self.get_access_token()}",
                    "Content-Type": "application/json"
                }
                
                payload = {
                    "operationName": google_operation_name
                }
                
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.post(url, json=payload, headers=headers)
                    
                    if response.status_code != 200:
                        logger.warning(f"Failed to check operation status: {response.status_code}")
                        continue
                    
                    result = response.json()
                    
                    if result.get("done"):
                        # Operation completed
                        if "response" in result:
                            # Success
                            await self.process_completed_operation(
                                operation_id, result["response"], operations_store, videos_store, request
                            )
                        else:
                            # Error
                            error_msg = result.get("error", {}).get("message", "Unknown error")
                            logger.error(f"Operation failed: {error_msg}")
                            operations_store[operation_id]["status"] = "failed"
                            operations_store[operation_id]["error"] = error_msg
                        return
            
            # Timeout
            operations_store[operation_id]["status"] = "failed"
            operations_store[operation_id]["error"] = "Operation timed out"
            
        except Exception as e:
            logger.error(f"Error polling operation status: {e}")
            operations_store[operation_id]["status"] = "failed"
            operations_store[operation_id]["error"] = str(e)

    async def process_completed_operation(
        self, 
        operation_id: str, 
        response_data: Dict, 
        operations_store: Dict, 
        videos_store: Dict,
        request
    ):
        """Process completed video generation operation"""
        try:
            operations_store[operation_id]["message"] = "Processing generated videos..."
            operations_store[operation_id]["progress"] = 95
            
            generated_samples = response_data.get("generatedSamples", [])
            video_ids = []
            
            for i, sample in enumerate(generated_samples):
                video_data = sample.get("video", {})
                video_uri = video_data.get("uri")
                
                if video_uri:
                    video_id = str(uuid.uuid4())
                    video_ids.append(video_id)
                    
                    # Store video metadata
                    videos_store[video_id] = {
                        "id": video_id,
                        "uri": video_uri,
                        "encoding": video_data.get("encoding", "video/mp4"),
                        "title": f"Generated Video {i + 1}",
                        "prompt": getattr(request, 'prompt', ''),
                        "type": operations_store[operation_id]["type"],
                        "created_at": datetime.utcnow().isoformat(),
                        "duration": request.durationSeconds
                    }
            
            # Update operation as completed
            operations_store[operation_id]["status"] = "completed"
            operations_store[operation_id]["progress"] = 100
            operations_store[operation_id]["message"] = f"Successfully generated {len(video_ids)} video(s)"
            operations_store[operation_id]["video_ids"] = video_ids
            
            logger.info(f"Operation {operation_id} completed successfully with {len(video_ids)} videos")
            
        except Exception as e:
            logger.error(f"Error processing completed operation: {e}")
            operations_store[operation_id]["status"] = "failed"
            operations_store[operation_id]["error"] = str(e)
