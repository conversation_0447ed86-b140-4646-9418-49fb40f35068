"""
Google GenAI client for direct Gemini API access
Uses the Google GenAI SDK with API key from environment variables
"""

import os
import logging
from typing import Optional, Dict, Any
from config import get_google_api_key

logger = logging.getLogger(__name__)

class GenAIClient:
    """Client for Google GenAI SDK"""
    
    def __init__(self):
        self.api_key = get_google_api_key()
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize the Google GenAI client"""
        if not self.api_key:
            logger.warning("Google API key not found. GenAI client will not be available.")
            return
        
        try:
            # Import and initialize the Google GenAI client
            from google import genai
            
            self.client = genai.Client(api_key=self.api_key)
            logger.info("Google GenAI client initialized successfully")
            
        except ImportError as e:
            logger.error(f"Failed to import google.genai: {e}")
            logger.error("Please install: pip install -q -U google-genai")
        except Exception as e:
            logger.error(f"Failed to initialize GenAI client: {e}")
    
    def is_available(self) -> bool:
        """Check if the GenAI client is available"""
        return self.client is not None
    
    async def generate_content(
        self, 
        prompt: str, 
        model: str = "gemini-2.0-flash"
    ) -> Optional[str]:
        """
        Generate content using Gemini API
        
        Args:
            prompt: Text prompt for generation
            model: Model to use (default: gemini-2.0-flash)
            
        Returns:
            Generated text or None if failed
        """
        if not self.is_available():
            logger.error("GenAI client not available")
            return None
        
        try:
            response = self.client.models.generate_content(
                model=model,
                contents=prompt
            )
            
            return response.text
            
        except Exception as e:
            logger.error(f"Failed to generate content: {e}")
            return None
    
    async def enhance_prompt(self, prompt: str) -> str:
        """
        Enhance a video generation prompt using Gemini
        
        Args:
            prompt: Original prompt
            
        Returns:
            Enhanced prompt or original if enhancement fails
        """
        if not self.is_available():
            logger.warning("GenAI client not available for prompt enhancement")
            return prompt
        
        enhancement_prompt = f"""
        Enhance this video generation prompt to be more detailed and cinematic while keeping the core concept:
        
        Original prompt: "{prompt}"
        
        Enhanced prompt should:
        - Add cinematic details (camera movements, lighting, atmosphere)
        - Include visual style descriptions
        - Maintain the original intent
        - Be suitable for AI video generation
        - Be concise but descriptive
        
        Return only the enhanced prompt, no explanations.
        """
        
        try:
            enhanced = await self.generate_content(enhancement_prompt)
            if enhanced and len(enhanced.strip()) > 0:
                logger.info("Prompt enhanced successfully")
                return enhanced.strip()
            else:
                logger.warning("Prompt enhancement returned empty result")
                return prompt
                
        except Exception as e:
            logger.error(f"Failed to enhance prompt: {e}")
            return prompt
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Test the GenAI client connection
        
        Returns:
            Dictionary with test results
        """
        result = {
            'api_key_available': bool(self.api_key),
            'client_initialized': self.is_available(),
            'test_successful': False,
            'error': None
        }
        
        if not self.is_available():
            result['error'] = "Client not initialized"
            return result
        
        try:
            # Test with a simple prompt
            test_response = self.client.models.generate_content(
                model="gemini-2.0-flash",
                contents="Say 'Hello' in one word."
            )
            
            if test_response and test_response.text:
                result['test_successful'] = True
                result['test_response'] = test_response.text.strip()
            else:
                result['error'] = "Empty response from API"
                
        except Exception as e:
            result['error'] = str(e)
        
        return result

# Global GenAI client instance
genai_client = GenAIClient()

def get_genai_client() -> GenAIClient:
    """Get the global GenAI client instance"""
    return genai_client
