from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class TextToVideoRequest(BaseModel):
    prompt: str = Field(..., description="Text prompt for video generation")
    negativePrompt: Optional[str] = Field("", description="Negative prompt to avoid certain content")
    durationSeconds: int = Field(8, ge=5, le=8, description="Video duration in seconds")
    aspectRatio: str = Field("16:9", description="Video aspect ratio")
    sampleCount: int = Field(1, ge=1, le=4, description="Number of videos to generate")
    enhancePrompt: bool = Field(True, description="Use Gemini to enhance the prompt")
    personGeneration: str = Field("allow_adult", description="Person generation setting")
    seed: Optional[int] = Field(None, description="Seed for deterministic generation")

class ImageToVideoRequest(BaseModel):
    prompt: Optional[str] = Field("", description="Optional text prompt to guide video generation")
    negativePrompt: Optional[str] = Field("", description="Negative prompt to avoid certain content")
    durationSeconds: int = Field(8, ge=5, le=8, description="Video duration in seconds")
    aspectRatio: str = Field("16:9", description="Video aspect ratio")
    sampleCount: int = Field(1, ge=1, le=4, description="Number of videos to generate")
    enhancePrompt: bool = Field(True, description="Use Gemini to enhance the prompt")
    personGeneration: str = Field("allow_adult", description="Person generation setting")
    seed: Optional[int] = Field(None, description="Seed for deterministic generation")

class VideoResponse(BaseModel):
    id: str
    uri: str
    encoding: str = "video/mp4"
    duration: Optional[float] = None
    thumbnail: Optional[str] = None
    title: Optional[str] = None
    prompt: Optional[str] = None
    type: str  # "text-to-video" or "image-to-video"
    created_at: str

class OperationStatusResponse(BaseModel):
    operation_id: str
    status: str  # "running", "completed", "failed"
    progress: int = 0  # 0-100
    message: str
    created_at: str
    videos: Optional[List[VideoResponse]] = None
    error: Optional[str] = None

class VideoListResponse(BaseModel):
    videos: List[VideoResponse]
    total: Optional[int] = None

class ErrorResponse(BaseModel):
    error: str
    detail: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
