import React, { useState, useEffect } from 'react'
import { RefreshCw, Trash2, Search, Filter, Calendar, Video } from 'lucide-react'
import toast from 'react-hot-toast'
import { videoAPI } from '../utils/api'
import VideoPlayer from './VideoPlayer'

const VideoGallery = () => {
  const [videos, setVideos] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('newest')
  const [filterBy, setFilterBy] = useState('all')

  useEffect(() => {
    loadVideos()
  }, [])

  const loadVideos = async () => {
    setLoading(true)
    try {
      const response = await videoAPI.getGeneratedVideos()
      setVideos(response.videos || [])
    } catch (error) {
      console.error('Error loading videos:', error)
      toast.error('Failed to load videos')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteVideo = async (videoId) => {
    if (!window.confirm('Are you sure you want to delete this video?')) {
      return
    }

    try {
      await videoAPI.deleteVideo(videoId)
      setVideos(prev => prev.filter(video => video.id !== videoId))
      toast.success('Video deleted successfully')
    } catch (error) {
      console.error('Error deleting video:', error)
      toast.error('Failed to delete video')
    }
  }

  const filteredAndSortedVideos = videos
    .filter(video => {
      // Search filter
      const matchesSearch = video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           video.prompt?.toLowerCase().includes(searchTerm.toLowerCase())
      
      // Type filter
      const matchesFilter = filterBy === 'all' || 
                           (filterBy === 'text-to-video' && video.type === 'text-to-video') ||
                           (filterBy === 'image-to-video' && video.type === 'image-to-video')
      
      return matchesSearch && matchesFilter
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.created_at) - new Date(a.created_at)
        case 'oldest':
          return new Date(a.created_at) - new Date(b.created_at)
        case 'title':
          return (a.title || '').localeCompare(b.title || '')
        default:
          return 0
      }
    })

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 text-gray-400 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading your videos...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Video Gallery</h2>
        <p className="text-gray-600">Browse and manage your generated videos</p>
      </div>

      {/* Controls */}
      <div className="card">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search videos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>
          </div>

          {/* Filter */}
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
              className="input-field min-w-[150px]"
            >
              <option value="all">All Types</option>
              <option value="text-to-video">Text to Video</option>
              <option value="image-to-video">Image to Video</option>
            </select>
          </div>

          {/* Sort */}
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="input-field min-w-[120px]"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="title">By Title</option>
            </select>
          </div>

          {/* Refresh */}
          <button
            onClick={loadVideos}
            className="btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Videos Grid */}
      {filteredAndSortedVideos.length === 0 ? (
        <div className="text-center py-12">
          <Video className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {videos.length === 0 ? 'No videos yet' : 'No videos match your search'}
          </h3>
          <p className="text-gray-600 mb-6">
            {videos.length === 0 
              ? 'Start generating videos using the Text to Video or Image to Video tabs'
              : 'Try adjusting your search terms or filters'
            }
          </p>
          {videos.length === 0 && (
            <button
              onClick={() => window.location.hash = '#text-to-video'}
              className="btn-primary"
            >
              Generate Your First Video
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAndSortedVideos.map((video) => (
            <div key={video.id} className="relative">
              <VideoPlayer
                video={video}
                title={video.title || `Video ${video.id}`}
                prompt={video.prompt}
              />
              
              {/* Video Metadata */}
              <div className="mt-2 px-2">
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      video.type === 'text-to-video' 
                        ? 'bg-blue-100 text-blue-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {video.type === 'text-to-video' ? 'Text→Video' : 'Image→Video'}
                    </span>
                    <span>{formatDate(video.created_at)}</span>
                  </div>
                  
                  <button
                    onClick={() => handleDeleteVideo(video.id)}
                    className="p-1 text-red-400 hover:text-red-600 transition-colors"
                    title="Delete video"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Stats */}
      {videos.length > 0 && (
        <div className="text-center text-sm text-gray-500">
          Showing {filteredAndSortedVideos.length} of {videos.length} videos
        </div>
      )}
    </div>
  )
}

export default VideoGallery
