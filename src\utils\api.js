import axios from 'axios'

const API_BASE_URL = '/api'

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
})

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`)
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

export const videoAPI = {
  // Text to video generation
  generateTextToVideo: async (data) => {
    const response = await api.post('/generate/text-to-video', data)
    return response.data
  },

  // Image to video generation
  generateImageToVideo: async (formData) => {
    const response = await api.post('/generate/image-to-video', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  // Check operation status
  checkOperationStatus: async (operationId) => {
    const response = await api.get(`/operations/${operationId}/status`)
    return response.data
  },

  // Get generated videos
  getGeneratedVideos: async () => {
    const response = await api.get('/videos')
    return response.data
  },

  // Download video
  downloadVideo: async (videoId) => {
    const response = await api.get(`/videos/${videoId}/download`, {
      responseType: 'blob',
    })
    return response.data
  },

  // Delete video
  deleteVideo: async (videoId) => {
    const response = await api.delete(`/videos/${videoId}`)
    return response.data
  },
}

export default api
