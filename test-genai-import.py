#!/usr/bin/env python3
"""
Test Google GenAI SDK import and basic functionality
"""

def test_genai_import():
    """Test the Google GenAI SDK import exactly as shown in the documentation"""
    print("🧪 Testing Google GenAI SDK Import")
    print("=" * 40)
    
    try:
        # Test the exact import from your screenshot
        print("Testing: from google import genai")
        from google import genai
        print("✅ Successfully imported: from google import genai")
        
        # Check available methods
        methods = [attr for attr in dir(genai) if not attr.startswith('_')]
        print(f"✅ Available methods: {len(methods)} methods found")
        print("   Key methods:")
        for method in methods[:10]:  # Show first 10 methods
            print(f"   • genai.{method}")
        
        # Test client creation (as shown in your screenshot)
        print("\nTesting: genai.Client(api_key='test')")
        try:
            client = genai.Client(api_key="test_key")
            print("✅ Successfully created genai.Client")
            print(f"   Client type: {type(client)}")
        except Exception as e:
            print(f"⚠️  Client creation test: {e}")
            print("   This may be expected without a valid API key")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def show_usage_example():
    """Show the exact usage example from the documentation"""
    print("\n" + "=" * 40)
    print("📖 Usage Example (from documentation)")
    print("=" * 40)
    
    example = '''
from google import genai

client = genai.Client(api_key="YOUR_API_KEY")

response = client.models.generate_content(
    model="gemini-2.0-flash", 
    contents="Explain how AI works in a few words"
)
print(response.text)
'''
    print(example)

if __name__ == "__main__":
    success = test_genai_import()
    
    if success:
        show_usage_example()
        print("🎉 Google GenAI SDK is properly installed and ready!")
        print("\n📝 Next steps:")
        print("   1. Get API key from: https://aistudio.google.com/app/apikey")
        print("   2. Set environment variable: GOOGLE_API_KEY=your_key")
        print("   3. Use the SDK in your application")
    else:
        print("❌ Google GenAI SDK test failed")
    
    print(f"\nPython version: {__import__('sys').version}")
    print(f"Virtual environment: {__import__('sys').prefix}")
