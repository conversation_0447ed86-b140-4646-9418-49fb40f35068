# Google Veo AI Video Generator - Quick Setup Guide

This guide will help you set up and run the Google Veo AI Video Generator application quickly.

## 🚀 Quick Start

### Prerequisites
- **Node.js 18+** and **npm**
- **Python 3.8+**
- **Google Cloud Project** with billing enabled
- **Google Veo API access** (limited preview)

### 1. Google Cloud Setup

#### Create Project and Enable APIs
```bash
# Create project
gcloud projects create your-project-id
gcloud config set project your-project-id

# Enable APIs
gcloud services enable aiplatform.googleapis.com
gcloud services enable storage.googleapis.com
```

#### Create Service Account
```bash
# Create service account
gcloud iam service-accounts create veo-service-account \
    --description="Service account for Veo video generation"

# Grant permissions
gcloud projects add-iam-policy-binding your-project-id \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/aiplatform.user"

# Create key file
gcloud iam service-accounts keys create veo-service-account-key.json \
    --iam-account=<EMAIL>
```

### 2. Application Setup

#### Clone and Install
```bash
# Install dependencies
npm install
cd backend && pip install -r requirements.txt && cd ..

# Configure environment
cp .env.example .env
# Edit .env with your Google Cloud settings
```

#### Configure .env File
```env
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=./veo-service-account-key.json
GOOGLE_CLOUD_STORAGE_BUCKET=your-storage-bucket-name  # Optional
VEO_MODEL_ID=veo-2.0-generate-001
```

### 3. Run the Application

#### Option A: Use Start Scripts
**Windows:**
```cmd
start.bat
```

**Linux/Mac:**
```bash
./start.sh
```

#### Option B: Manual Start
**Terminal 1 (Backend):**
```bash
cd backend
python main.py
```

**Terminal 2 (Frontend):**
```bash
npm run dev
```

### 4. Verify Setup

```bash
# Check setup
python setup-check.py

# Test backend
python test-backend.py
```

### 5. Access Application

Open your browser and go to: **http://localhost:3000**

## 🎯 Key Features

### Text-to-Video Generation
1. Navigate to "Text to Video" tab
2. Enter descriptive prompt
3. Configure settings (duration, aspect ratio, etc.)
4. Click "Generate Video"
5. Monitor progress and download results

### Image-to-Video Generation
1. Navigate to "Image to Video" tab
2. Upload image (JPEG, PNG, WebP)
3. Optionally add text prompt
4. Configure settings
5. Generate and download videos

### Video Gallery
- Browse all generated videos
- Search and filter
- Download and delete videos

## ⚙️ Configuration Options

### Video Generation Parameters
- **Duration**: 5-8 seconds
- **Aspect Ratio**: 16:9 (landscape) or 9:16 (portrait)
- **Sample Count**: 1-4 videos per generation
- **Person Generation**: Allow adults or disable people
- **Prompt Enhancement**: Use Gemini to improve prompts

### Environment Variables
| Variable | Description | Required |
|----------|-------------|----------|
| `GOOGLE_CLOUD_PROJECT` | Your Google Cloud project ID | Yes |
| `GOOGLE_APPLICATION_CREDENTIALS` | Path to service account key | Yes |
| `GOOGLE_CLOUD_LOCATION` | Vertex AI location | No (default: us-central1) |
| `GOOGLE_CLOUD_STORAGE_BUCKET` | Storage bucket for videos | No |
| `VEO_MODEL_ID` | Veo model version | No (default: veo-2.0-generate-001) |

## 🔧 Troubleshooting

### Common Issues

#### Authentication Errors
- Verify service account key path in `.env`
- Check IAM permissions for service account
- Ensure APIs are enabled in Google Cloud

#### "Veo API access denied"
- Google Veo is in limited preview
- Request access through Google Cloud Console or Google AI Studio
- Check if your project has Veo API access

#### Backend connection errors
- Ensure backend is running on port 8000
- Check firewall settings
- Verify Python dependencies are installed

#### Video generation fails
- Check Google Cloud project billing
- Verify Veo API quotas and limits
- Review prompt content policies

### Getting Help

1. **Check logs**: Backend console output and browser developer tools
2. **Verify setup**: Run `python setup-check.py`
3. **Test backend**: Run `python test-backend.py`
4. **Google Cloud logs**: Check Cloud Logging in Google Cloud Console

## 📚 Additional Resources

- [Google Veo Documentation](https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/veo-video-generation)
- [Google Cloud AI Platform](https://cloud.google.com/vertex-ai)
- [Google AI Studio](https://aistudio.google.com/)
- [Vertex AI Pricing](https://cloud.google.com/vertex-ai/pricing)

## 🔐 Security Notes

- Keep service account keys secure
- Don't commit credentials to version control
- Use environment variables for all secrets
- Implement proper authentication in production
- Set up CORS policies appropriately

## 🚀 Production Deployment

For production deployment:
1. Use Google Cloud Run or similar serverless platform
2. Implement proper authentication and authorization
3. Set up monitoring and logging
4. Use Cloud SQL for persistent storage
5. Implement rate limiting and quotas
6. Set up proper CORS and security headers

---

**Need help?** Check the full README.md for detailed documentation and troubleshooting.
