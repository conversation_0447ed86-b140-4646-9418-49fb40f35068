import React, { useState, useEffect } from 'react'
import { Loader2, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react'
import { videoAPI } from '../utils/api'

const OperationStatus = ({ operationId, onComplete, onError }) => {
  const [status, setStatus] = useState('running')
  const [progress, setProgress] = useState(0)
  const [message, setMessage] = useState('Initializing video generation...')
  const [elapsedTime, setElapsedTime] = useState(0)

  useEffect(() => {
    if (!operationId) return

    let pollInterval
    let timeInterval

    const pollStatus = async () => {
      try {
        const response = await videoAPI.checkOperationStatus(operationId)
        
        setStatus(response.status)
        setMessage(response.message || 'Processing...')
        
        if (response.progress !== undefined) {
          setProgress(response.progress)
        }

        if (response.status === 'completed') {
          clearInterval(pollInterval)
          clearInterval(timeInterval)
          onComplete(response.videos || [])
        } else if (response.status === 'failed' || response.status === 'error') {
          clearInterval(pollInterval)
          clearInterval(timeInterval)
          onError(response.error || 'Video generation failed')
        }
      } catch (error) {
        console.error('Error polling operation status:', error)
        // Continue polling unless it's a critical error
        if (error.response?.status === 404) {
          clearInterval(pollInterval)
          clearInterval(timeInterval)
          onError('Operation not found')
        }
      }
    }

    // Start polling immediately
    pollStatus()
    
    // Poll every 5 seconds
    pollInterval = setInterval(pollStatus, 5000)
    
    // Update elapsed time every second
    timeInterval = setInterval(() => {
      setElapsedTime(prev => prev + 1)
    }, 1000)

    return () => {
      clearInterval(pollInterval)
      clearInterval(timeInterval)
    }
  }, [operationId, onComplete, onError])

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-6 w-6 text-green-500" />
      case 'failed':
      case 'error':
        return <XCircle className="h-6 w-6 text-red-500" />
      case 'running':
      case 'processing':
        return <Loader2 className="h-6 w-6 text-blue-500 animate-spin" />
      default:
        return <Clock className="h-6 w-6 text-gray-500" />
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 border-green-200'
      case 'failed':
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'running':
      case 'processing':
        return 'bg-blue-50 border-blue-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  const getProgressBarColor = () => {
    switch (status) {
      case 'completed':
        return 'bg-green-500'
      case 'failed':
      case 'error':
        return 'bg-red-500'
      default:
        return 'bg-blue-500'
    }
  }

  return (
    <div className={`card ${getStatusColor()}`}>
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          {getStatusIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium text-gray-900">
              Video Generation Status
            </h3>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Clock className="h-4 w-4" />
              <span>{formatTime(elapsedTime)}</span>
            </div>
          </div>
          
          <p className="text-sm text-gray-600 mb-3">{message}</p>
          
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor()}`}
              style={{ width: `${Math.max(progress, status === 'running' ? 10 : 0)}%` }}
            />
          </div>
          
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Operation ID: {operationId}</span>
            <span>{progress}% complete</span>
          </div>
          
          {/* Status Messages */}
          {status === 'running' && (
            <div className="mt-3 p-3 bg-blue-100 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-blue-600" />
                <p className="text-sm text-blue-800">
                  Video generation typically takes 2-5 minutes. Please keep this page open.
                </p>
              </div>
            </div>
          )}
          
          {status === 'failed' && (
            <div className="mt-3 p-3 bg-red-100 rounded-lg">
              <div className="flex items-center space-x-2">
                <XCircle className="h-4 w-4 text-red-600" />
                <p className="text-sm text-red-800">
                  Generation failed. Please try again with different parameters.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default OperationStatus
