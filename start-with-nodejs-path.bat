@echo off
setlocal enabledelayedexpansion

REM Add Node.js to PATH manually
set "PATH=%PATH%;C:\Program Files\nodejs"

echo ========================================
echo Google Veo AI Video Generator - Startup
echo ========================================
echo.

REM Check if Node.js is installed (with manual PATH)
echo [1/8] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Node.js is not installed or not in PATH
    echo    Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js found: !NODE_VERSION!

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: npm is not installed or not in PATH
    echo    npm should be installed with Node.js
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm found: !NPM_VERSION!

REM Check if Python is installed
echo.
echo [2/8] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Python is not installed or not in PATH
    echo    Please install Python from https://python.org/
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ✅ Python found: !PYTHON_VERSION!

REM Check if .env file exists
echo.
echo [3/8] Checking environment configuration...
if not exist .env (
    echo ⚠️  Warning: .env file not found
    echo    Please copy .env.example to .env and configure your Google Cloud settings
    echo.
    if exist .env.example (
        echo    Copying .env.example to .env...
        copy .env.example .env >nul
        if %errorlevel% equ 0 (
            echo ✅ Created .env file from template
            echo ⚠️  Please edit .env file with your Google Cloud configuration before continuing
        ) else (
            echo ❌ Failed to copy .env.example
        )
        echo.
    ) else (
        echo ❌ .env.example file not found
        echo    Please create .env file manually with your Google Cloud configuration
        echo.
    )
) else (
    echo ✅ Environment file (.env) found
)

REM Virtual Environment Management
echo.
echo [4/8] Setting up Python virtual environment...

REM Check if virtual environment exists
if not exist backend\venv (
    echo ⚠️  Virtual environment not found, creating...
    cd backend
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ Error: Failed to create virtual environment
        echo    Please ensure Python is properly installed and has venv module
        cd ..
        echo.
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created successfully
    cd ..
) else (
    echo ✅ Virtual environment found at backend\venv
)

REM Install/update Python dependencies
echo.
echo [5/8] Installing Python dependencies...
cd backend
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ Error: Failed to activate virtual environment
    echo    Virtual environment may be corrupted, please delete backend\venv and try again
    cd ..
    echo.
    pause
    exit /b 1
)
echo ✅ Virtual environment activated successfully

REM Check if requirements.txt exists
if not exist requirements.txt (
    echo ❌ Error: requirements.txt not found in backend directory
    echo    Please ensure requirements.txt exists
    call venv\Scripts\deactivate.bat >nul 2>&1
    cd ..
    echo.
    pause
    exit /b 1
)

REM Install/update dependencies
echo    Installing/updating Python dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Error: Failed to install Python dependencies
    echo    Please check your internet connection and requirements.txt file
    call venv\Scripts\deactivate.bat >nul 2>&1
    cd ..
    echo.
    pause
    exit /b 1
)
echo ✅ Python dependencies installed/updated successfully

cd ..

REM Install frontend dependencies
echo.
echo [6/8] Installing frontend dependencies...
echo    Installing Node.js packages...
npm install
if %errorlevel% neq 0 (
    echo ❌ Error: Failed to install frontend dependencies
    echo    Please check your internet connection and package.json file
    echo.
    pause
    exit /b 1
)
echo ✅ Frontend dependencies installed successfully

REM Start the servers
echo.
echo [7/8] Starting development servers...
echo.

REM Start backend server in background
echo 🚀 Starting backend server (with virtual environment)...
start "Backend Server" cmd /c "cd backend && call venv\Scripts\activate.bat && python main.py"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend development server
echo 🚀 Starting frontend development server...
echo.
echo ⏳ Please wait while the development server starts...
echo    This may take a few moments on first run...
echo.

npm run dev

REM This will keep the frontend server running
echo.
echo [8/8] Servers started successfully!
echo.
echo ========================================
echo   🎉 Google Veo AI Video Generator
echo ========================================
echo.
echo 🌐 Application URLs:
echo    • Backend API: http://localhost:8000
echo    • Frontend UI:  http://localhost:3000
echo    • API Docs:    http://localhost:8000/docs
echo    • Health Check: http://localhost:8000/api/health
echo.
echo 💡 Tips:
echo    • Press Ctrl+C to stop the frontend server
echo    • Backend server runs in a separate window
echo    • Check the health endpoint to verify API key configuration
echo.
echo Press any key to close this window...
pause >nul
